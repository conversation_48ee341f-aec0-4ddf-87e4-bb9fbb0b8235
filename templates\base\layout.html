{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ SEO_TITLE }}{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'img/favicons/favicon.ico' %}">
    <link rel="icon" type="image/svg+xml" href="{% static 'img/favicons/favicon.svg' %}">
    <link rel="apple-touch-icon" href="{% static 'img/favicons/favicon.ico' %}">

    {% if GOOGLE_ANALYTICS_ID %}
    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id={{ GOOGLE_ANALYTICS_ID }}"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', '{{ GOOGLE_ANALYTICS_ID }}');
    </script>
    {% endif %}

    <!-- SEO Meta tags -->
    <meta name="description" content="{% block meta_description %}{{ SEO_DESCRIPTION }}{% endblock %}">
    <meta name="keywords" content="{% block meta_keywords %}{{ SEO_KEYWORDS }}{% endblock %}">
    <meta name="author" content="{{ SEO_TITLE }}">
    <meta name="robots" content="{% block meta_robots %}index, follow{% endblock %}">
    <link rel="canonical" href="{{ request.build_absolute_uri }}">
    {% if GOOGLE_VERIFICATION_TAG %}
    <meta name="google-site-verification" content="{{ GOOGLE_VERIFICATION_TAG }}">
    {% endif %}

    <!-- Open Graph tags for social sharing -->
    <meta property="og:title" content="{% block og_title %}{{ self.title }}{% endblock %}">
    <meta property="og:description" content="{% block og_description %}{{ self.meta_description }}{% endblock %}">
    <meta property="og:url" content="{{ request.build_absolute_uri }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Corporate Prompt Master">

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.3/font/bootstrap-icons.css">

    <!-- Global CSS Framework -->
    <link rel="stylesheet" href="{% static 'css/mobile-fixes.css' %}">
    <link rel="stylesheet" href="{% static 'css/global-framework.css' %}">

    <!-- Enhanced Base Layout CSS -->
    <style>
        :root {
            --sidebar-width: 280px;
            --header-height: 64px;
            --sidebar-collapsed-width: 80px;
        }

        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background: var(--bg-light);
            font-family: var(--font-family-base);
        }

        .main-header {
            height: var(--header-height);
            z-index: 1030;
            background: var(--bg-white);
            border-bottom: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
        }

        .main-container {
            display: flex;
            flex: 1;
            min-height: calc(100vh - var(--header-height));
        }

        .sidebar {
            width: var(--sidebar-width);
            background: var(--bg-white);
            border-right: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            transition: var(--transition-base);
            position: fixed;
            top: var(--header-height);
            left: 0;
            height: calc(100vh - var(--header-height));
            overflow-y: auto;
            z-index: 1020;
        }

        .main-content {
            flex: 1;
            margin-left: var(--sidebar-width);
            padding: var(--space-lg);
            background: var(--bg-light);
            min-height: calc(100vh - var(--header-height));
            transition: var(--transition-base);
        }

        /* Sidebar collapsed state */
        .sidebar-collapsed .sidebar {
            width: var(--sidebar-collapsed-width);
        }

        .sidebar-collapsed .main-content {
            margin-left: var(--sidebar-collapsed-width);
        }

        /* For pages that don't need a sidebar */
        .no-sidebar .main-content {
            margin-left: 0;
        }

        .no-sidebar .sidebar {
            display: none;
        }

        /* Responsive adjustments */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                width: var(--sidebar-width);
            }

            .sidebar.show {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
            }

            .sidebar-toggle {
                display: block;
            }
        }

        @media (max-width: 576px) {
            .main-content {
                padding: var(--space-md);
            }
        }

        /* Loading overlay */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
            opacity: 0;
            visibility: hidden;
            transition: var(--transition-base);
        }

        .loading-overlay.show {
            opacity: 1;
            visibility: visible;
        }
    </style>

    <!-- Custom CSS -->
    {% block extra_css %}
    {% endblock %}
</head>
<body class="{% block body_class %}{% endblock %}">
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="text-center">
            <div class="spinner-border text-primary mb-3" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <div class="text-muted">Loading...</div>
        </div>
    </div>

    <!-- Header -->
    {% include 'base/header.html' %}

    <div class="main-container">
        <!-- Sidebar (only shown if not explicitly hidden) -->
        {% if not hide_sidebar %}
        {% include 'base/sidebar.html' %}
        {% endif %}

        <!-- Main Content -->
        <div class="main-content">
            <!-- Breadcrumb -->
            {% block breadcrumb %}
            {% endblock %}

            <!-- Page Content -->
            {% block content %}
            <!-- Main content goes here -->
            {% endblock %}
        </div>
    </div>

    <!-- Custom Mobile Sidebar Accordion -->
    <script src="{% static 'js/mobile-sidebar-accordion.js' %}" defer></script>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.4.min.js"></script>

    <!-- Base Layout JS -->
    <script>
        // Toggle sidebar on mobile
        document.addEventListener('DOMContentLoaded', function() {
            const toggleButton = document.querySelector('.navbar-toggler');
            const sidebar = document.querySelector('.sidebar');

            if (toggleButton && sidebar) {
                toggleButton.addEventListener('click', function() {
                    sidebar.classList.toggle('show');
                });
            }
        });
    </script>

    <!-- Custom JS -->
    {% block extra_js %}{% endblock %}

    <!-- Structured Data for SEO -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Corporate Prompt Master",
        "url": "{{ request.scheme }}://{{ request.get_host }}/",
        "description": "AI-powered game for corporate training and prompt engineering skills",
        "potentialAction": {
            "@type": "SearchAction",
            "target": "{{ request.scheme }}://{{ request.get_host }}/search?q={search_term_string}",
            "query-input": "required name=search_term_string"
        }
    }
    </script>
    {% block structured_data %}{% endblock %}
</body>
</html>
