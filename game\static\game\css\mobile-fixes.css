/**
 * Mobile Fixes for Corporate Prompt Master Game
 * Original game1 mobile mode with current color theme
 */

/* Prevent horizontal scrolling on mobile and remove unnecessary scrollbars */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Remove any body padding on mobile */
@media (max-width: 992px) {
    body {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
}

/* MOBILE LAYOUT OVERRIDES - HIGHEST PRIORITY */
@media (max-width: 992px) {
    /* RESET BODY CLASSES ON MOBILE */
    body {
        /* Remove any default sidebar classes */
        overflow-x: hidden !important;
    }

    body:not(.sidebar-visible):not(.right-sidebar-visible) {
        /* Ensure body is normal when no sidebars are visible */
        overflow: auto !important;
        position: static !important;
        width: 100% !important;
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure html element doesn't create white space */
    html {
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden !important;
    }

    /* ===== LEFT SIDEBAR (NAVIGATION & CONTACT INFO) ===== */
    body .sidebar,
    body #sidebar,
    body .left-sidebar,
    .game-container .sidebar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 85vw !important; /* Professional mobile width - not full screen */
        max-width: 320px !important; /* Max width for larger phones */
        height: 100vh !important;
        z-index: 10001 !important;
        background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
        overflow-y: auto !important;
        padding: 0 !important; /* Remove padding, add it to content */
        border: none !important;
        box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3) !important;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: translateX(-100%) !important; /* Hidden by default */
    }

    /* Show left sidebar when body has sidebar-visible class */
    body.sidebar-visible .sidebar,
    body.sidebar-visible .left-sidebar,
    body.sidebar-visible #sidebar {
        transform: translateX(0) !important;
    }

    /* Add close button to left sidebar on mobile only */
    .sidebar::before {
        content: '✕' !important;
        position: absolute !important;
        top: 20px !important;
        right: 20px !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        color: white !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 18px !important;
        font-weight: bold !important;
        cursor: pointer !important;
        z-index: 10002 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
        transition: all 0.3s ease !important;
    }

    .sidebar::before:hover {
        background: var(--primary-dark) !important;
        transform: scale(1.1) !important;
    }

    /* ===== RIGHT SIDEBAR (ORGANIZATION CHART & CAREER PATH) ===== */
    body .right-sidebar,
    body #right-sidebar,
    .game-container .right-sidebar {
        position: fixed !important;
        top: 0 !important;
        right: 0 !important;
        width: 85vw !important;
        max-width: 320px !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%) !important;
        overflow-y: auto !important;
        padding: 0 !important;
        border: none !important;
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3) !important;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        transform: translateX(100%) !important; /* Hidden by default */
    }

    /* Show right sidebar when body has right-sidebar-visible class */
    body.right-sidebar-visible .right-sidebar {
        transform: translateX(0) !important;
    }

    /* Add close button to right sidebar on mobile */
    .right-sidebar::before {
        content: '✕' !important;
        position: absolute !important;
        top: 20px !important;
        right: 20px !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        color: white !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        font-size: 18px !important;
        font-weight: bold !important;
        cursor: pointer !important;
        z-index: 10000 !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
        transition: all 0.3s ease !important;
    }

    .right-sidebar::before:hover {
        background: var(--primary-dark) !important;
        transform: scale(1.1) !important;
    }

    /* Add padding to sidebar content */
    .sidebar > *,
    .right-sidebar > * {
        padding-left: 20px !important;
        padding-right: 20px !important;
    }

    .sidebar-header,
    .right-sidebar-header {
        padding: 20px !important;
        margin-bottom: 0 !important;
    }

    /* MAIN CONTENT TAKES FULL WIDTH */
    body .main-content,
    body .game-container,
    body .content-wrapper,
    .game-container .main-content {
        width: 100vw !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        left: 0 !important;
        right: 0 !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* Custom scrollbar styling for mobile */
    ::-webkit-scrollbar {
        width: 0px !important;
        background: transparent !important;
    }

    /* For Firefox */
    * {
        scrollbar-width: none !important;
    }

    /* MOBILE HEADER LAYOUT - ENSURE RIGHT TOGGLE AT FAR RIGHT */
    .header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 10px 15px !important;
        background: var(--bg-primary) !important;
        border-bottom: 1px solid var(--border-primary) !important;
    }

    .header-left {
        flex: 0 0 auto !important;
    }

    .header-center {
        flex: 1 1 auto !important;
        text-align: center !important;
    }

    .header-right {
        flex: 0 0 auto !important;
        margin-left: auto !important;
    }

    /* MOBILE SIDEBAR CONTENT STYLING */
    .sidebar .logo h1,
    .right-sidebar .right-sidebar-title {
        font-size: 1.5rem !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
        color: var(--text-primary) !important;
    }

    .sidebar .character-info,
    .sidebar .instructions,
    .right-sidebar .org-chart-container,
    .right-sidebar .role-progression-container {
        margin-bottom: 1.5rem !important;
        padding: 15px !important;
        background: var(--bg-card) !important;
        border-radius: var(--radius-md) !important;
        border: 1px solid var(--border-primary) !important;
    }

    /* Main content mobile layout - full screen when sidebars hidden */
    .main-content {
        flex: 1 !important;
        height: 100vh !important;
        min-height: 0 !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        display: flex !important;
        flex-direction: column !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        position: relative !important;
    }

    /* Adjust main content when sidebar is visible */
    .sidebar-visible .main-content {
        height: 100vh !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
    }

    /* Messages container mobile */
    .messages-container {
        flex: 1 !important;
        overflow-y: auto !important;
        padding: 1rem !important;
        background: var(--bg-primary) !important;
        height: auto !important;
        min-height: 0 !important;
    }

    /* Input area mobile - fixed at bottom */
    .input-area {
        flex-shrink: 0 !important;
        padding: 1rem !important;
        background: var(--bg-secondary) !important;
        border-top: 1px solid var(--border-primary) !important;
        min-height: 80px !important;
        max-height: 80px !important;
    }

    /* Form controls mobile */
    .prompt-input, .response-editor {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
        padding: 0.75rem !important;
        border-radius: 8px !important;
        border: 2px solid var(--border-primary) !important;
        width: 100% !important;
        resize: vertical !important;
        background: var(--bg-card) !important;
        color: var(--text-primary) !important;
    }

    .prompt-input:focus, .response-editor:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        outline: none !important;
    }

    /* Buttons mobile */
    .btn, .preview-button, .primary-button, .secondary-button {
        min-height: 44px !important;
        padding: 0.75rem 1.25rem !important;
        font-size: 1rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        touch-action: manipulation !important;
    }

    /* Button groups mobile */
    .preview-actions, .edit-actions {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
        margin-top: 1rem !important;
    }

    /* Messages mobile */
    .message {
        max-width: 95% !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
    }

    .message-content {
        padding: 0.75rem 1rem !important;
        font-size: 0.95rem !important;
        line-height: 1.4 !important;
    }

    /* Mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
    }

    .mobile-sidebar-toggle:hover,
    .mobile-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    .mobile-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* MOBILE RIGHT SIDEBAR TOGGLE - MATCHING LEFT SIDEBAR STYLE */
    .right-sidebar-toggle,
    #right-sidebar-toggle,
    div.right-sidebar-toggle,
    div#right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
        position: relative !important;
        z-index: 1002 !important;
    }

    .right-sidebar-toggle:hover,
    .right-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    .right-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* Keep buttons side-by-side on mobile but adjust sizing */
    .button-row {
        flex-direction: row;
        gap: 8px;
    }

    #restart-game-button-main.btn {
        min-width: 120px;
        flex: 0 0 auto;
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    #preview-button.btn {
        flex: 1;
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    /* Preview container mobile */
    .preview-container {
        background: var(--bg-card) !important;
        border: 1px solid var(--border-primary) !important;
        border-radius: var(--radius-md) !important;
    }

    .preview-header {
        background: var(--bg-secondary) !important;
        color: var(--text-primary) !important;
        border-bottom: 1px solid var(--border-primary) !important;
    }

    .preview-content {
        background: var(--bg-card) !important;
        color: var(--text-primary) !important;
    }

    /* Modal mobile */
    .modal-content {
        background: var(--bg-card) !important;
        color: var(--text-primary) !important;
        border: 1px solid var(--border-primary) !important;
    }

    .modal-buttons .btn {
        width: 100% !important;
    }
}

/* Small mobile devices (phones) */
@media (max-width: 576px) {
    .sidebar {
        max-height: 200px !important;
        padding: 0.5rem !important;
    }

    .main-content {
        height: calc(100vh - 200px) !important;
    }

    .header {
        padding: 0.5rem !important;
    }

    .messages-container {
        padding: 0.75rem !important;
    }

    .input-area {
        padding: 0.75rem !important;
    }

    .prompt-input, .response-editor {
        height: 70px !important;
        padding: 0.5rem !important;
    }

    .btn, .preview-button, .primary-button, .secondary-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
    }

    .character-info {
        padding: 0.5rem !important;
    }

    .character-name {
        font-size: 0.9rem !important;
    }

    .character-title {
        font-size: 0.8rem !important;
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    /* Larger touch targets */
    .btn, button, .mobile-sidebar-toggle {
        min-height: 48px !important;
        min-width: 48px !important;
    }

    /* Prevent text selection on UI elements */
    .mobile-sidebar-toggle, .btn, button {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Improve scrolling on touch devices */
    .messages-container, .sidebar {
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;
    }
}

/* Hide close buttons on desktop */
@media (min-width: 993px) {
    .sidebar::before,
    .right-sidebar::before {
        display: none !important;
    }
}
