// Mobile Sidebar Accordion
// Converts sidebar sections into accordion panels on small screens
(function () {
    function setupAccordion() {
        console.log('[Accordion] running setup, viewport width:', window.innerWidth);
        if (window.innerWidth > 768) {
            // Ensure everything visible on larger screens
            document.querySelectorAll('.sidebar .mb-4 ul').forEach(function (ul) {
                ul.style.display = '';
            });
            return;
        }

        document.querySelectorAll('.sidebar .mb-4').forEach(function (section, idx) {
            console.log('[Accordion] processing section', idx);
            const header = section.querySelector('h6, .sidebar-heading');
            const list = section.querySelector('ul');
            if (!header || !list) return;

            // Skip if already initialised
            if (header.dataset.accordionInitialised) return;
            header.dataset.accordionInitialised = 'true';

            header.style.cursor = 'pointer';
            header.setAttribute('aria-expanded', 'false');
            list.style.display = 'none';

            header.addEventListener('click', function () {
                console.log('[Accordion] toggle click', idx);
                const expanded = header.getAttribute('aria-expanded') === 'true';
                header.setAttribute('aria-expanded', (!expanded).toString());
                list.style.display = expanded ? 'none' : 'block';
            });
        });
    }

    document.addEventListener('DOMContentLoaded', setupAccordion);
    window.addEventListener('resize', setupAccordion);
})();
