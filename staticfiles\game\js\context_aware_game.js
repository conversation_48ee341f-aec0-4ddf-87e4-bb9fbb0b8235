// Corporate Prompt Master Game Logic - Context Aware Edition

// Game state
const gameState = {
    messages: [],
    currentRole: 'applicant',
    performanceScore: 0,
    challengesCompleted: 0,
    roleChallengesCompleted: 0, // Tracks challenges completed in current role
    characters: {},
    currentStep: 'prompt', // 'prompt', 'preview', 'edit', 'feedback'
    previewData: null,
    gameCompleted: false,
    currentManager: 'hr',
    currentTask: 'cover_letter',
    completedRoles: [], // Tracks roles that have been completed
    taskCompleted: false, // Flag to indicate a task was just completed
    nextTaskPending: false, // Flag to indicate a next task is pending
    firstTaskPending: true // Flag to indicate first task is pending
};

// Character colors for avatars
const characterColors = {
    hr: '#4a86e8',
    manager: '#43a047',
    vp: '#f57c00',
    coo: '#7b1fa2',
    ceo: '#c62828',
    board: '#5d4037',
    user: '#2196f3',
    ai: '#9c27b0',
    system: '#607d8b'
};

// DOM Elements
const elements = {
    roleProgressionContainer: document.getElementById('role-progression-container'),
    messagesContainer: document.getElementById('messages-container'),
    promptInput: document.getElementById('prompt-input'),
    previewButton: document.getElementById('preview-button'),
    promptContainer: document.getElementById('prompt-container'),
    previewContainer: document.getElementById('preview-container'),
    previewContent: document.getElementById('preview-content'),
    editResponseContainer: document.getElementById('edit-response-container'),
    responseEditor: document.getElementById('response-editor'),
    editPromptButton: document.getElementById('edit-prompt-button'),
    editResponseButton: document.getElementById('edit-response-button'),
    submitButton: document.getElementById('submit-button'),
    backToPreviewButton: document.getElementById('back-to-preview-button'),
    submitEditedButton: document.getElementById('submit-edited-button'),
    currentRole: document.getElementById('current-role'),
    roleDisplay: document.getElementById('role-display'),
    performanceScore: document.getElementById('performance-score'),
    challengesCompleted: document.getElementById('challenges-completed'),
    roleProgress: document.getElementById('role-progress'),
    progressBar: document.getElementById('progress-bar'),
    characterAvatar: document.getElementById('character-avatar'),
    characterName: document.getElementById('character-name'),
    characterTitle: document.getElementById('character-title'),
    qualityIndicator: document.getElementById('quality-indicator'),
    loadingOverlay: document.getElementById('loading-overlay'),
    gameCompleteModal: document.getElementById('game-complete-modal'),
    finalScore: document.getElementById('final-score'),
    restartGameButton: document.getElementById('restart-game-button'),
    closeModalButton: document.getElementById('close-modal-button'),
    currentManager: document.getElementById('current-manager'),
    currentTask: document.getElementById('current-task'),
    orgChart: document.getElementById('org-chart'),
    similarityContainer: document.getElementById('similarity-container'),
    similarityScore: document.getElementById('similarity-score'),
    feedbackDetailsList: document.getElementById('feedback-details-list'),
    sectionScoresContainer: document.getElementById('section-scores-container'),
    sectionScoresList: document.getElementById('section-scores-list'),
    enhancedEvaluationContainer: document.getElementById('enhanced-evaluation-container'),
    meetsRequirementsIndicator: document.getElementById('meets-requirements-indicator'),
    improvementFeedback: document.getElementById('improvement-feedback'),
    // Prompt evaluation elements
    promptEvaluationContainer: document.getElementById('prompt-evaluation-container'),
    promptEvaluationSummary: document.getElementById('prompt-evaluation-summary'),
    dimensionsGrid: document.getElementById('dimensions-grid'),
    promptImprovementList: document.getElementById('prompt-improvement-list'),

    // Zoom preview elements
    zoomPreviewButton: document.getElementById('zoom-preview-button'),
    zoomPreviewModal: document.getElementById('zoom-preview-modal'),
    zoomedPreviewContent: document.getElementById('zoomed-preview-content'),
    closeZoomModalButton: document.getElementById('close-zoom-modal-button'),

    // Role progression elements
    toggleRoleProgressionButton: document.getElementById('toggle-role-progression'),
    roleProgressionContent: document.getElementById('role-progression-content'),

    // Task failure modal elements
    taskFailureModal: document.getElementById('task-failure-modal'),
    failureFeedback: document.getElementById('failure-feedback'),
    retryTaskButton: document.getElementById('retry-task-button')
};



// Initialize the game
async function initGame() {
    console.log('Initializing game...');

    // Add event listeners
    elements.previewButton.addEventListener('click', handlePreviewPrompt);
    elements.editPromptButton.addEventListener('click', () => setStep('prompt'));
    elements.editResponseButton.addEventListener('click', () => setStep('edit'));
    elements.submitButton.addEventListener('click', handleSubmitResponse);
    elements.backToPreviewButton.addEventListener('click', () => setStep('preview'));
    elements.submitEditedButton.addEventListener('click', handleSubmitEditedResponse);
    elements.restartGameButton.addEventListener('click', showRestartConfirmation);
    elements.closeModalButton.addEventListener('click', () => elements.gameCompleteModal.classList.add('hidden'));

    // Add event listeners for restart confirmation modal
    const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
    const cancelRestartButton = document.getElementById('cancel-restart-button');
    const confirmRestartButton = document.getElementById('confirm-restart-button');

    if (restartConfirmationModal && cancelRestartButton && confirmRestartButton) {
        cancelRestartButton.addEventListener('click', () => restartConfirmationModal.classList.add('hidden'));
        confirmRestartButton.addEventListener('click', () => {
            restartConfirmationModal.classList.add('hidden');
            startGame();
        });
    }

    // Add event listeners for the continue and restart game buttons in the sidebar
    const continueGameButton = document.getElementById('continue-game-button');
    if (continueGameButton) {
        continueGameButton.addEventListener('click', continueGame);
    }

    const restartGameButtonSidebar = document.getElementById('restart-game-button-sidebar');
    if (restartGameButtonSidebar) {
        restartGameButtonSidebar.addEventListener('click', showRestartConfirmation);
    }

    // Add event listener for the main restart game button
    const restartGameButtonMain = document.getElementById('restart-game-button-main');
    if (restartGameButtonMain) {
        restartGameButtonMain.addEventListener('click', showRestartConfirmation);
    }

    // Add login prompt modal functionality
    const loginPromptModal = document.getElementById('login-prompt-modal');
    const continueAsGuestButton = document.getElementById('continue-as-guest-button');

    if (loginPromptModal && continueAsGuestButton) {
        continueAsGuestButton.addEventListener('click', () => {
            loginPromptModal.classList.add('hidden');
            localStorage.setItem('continue_as_guest', 'true');
        });
    }

    // Add zoom preview functionality
    if (elements.zoomPreviewButton) {
        elements.zoomPreviewButton.addEventListener('click', handleZoomPreview);
    }

    if (elements.closeZoomModalButton) {
        elements.closeZoomModalButton.addEventListener('click', () => elements.zoomPreviewModal.classList.add('hidden'));
    }

    // Debug functionality removed

    // Add task failure modal functionality
    if (elements.retryTaskButton) {
        elements.retryTaskButton.addEventListener('click', () => {
            // Hide the modal
            elements.taskFailureModal.classList.add('hidden');

            // Resend the task message (this will also set the step to 'prompt')
            resendTaskMessage();

            // Clear any existing input in the prompt field
            if (elements.promptInput) {
                elements.promptInput.value = '';
            }
        });
    }

    // Add role progression toggle functionality
    if (elements.toggleRoleProgressionButton) {
        elements.toggleRoleProgressionButton.addEventListener('click', toggleRoleProgression);

        // Check if user preference is stored in localStorage
        const roleProgressionPref = localStorage.getItem('roleProgressionCollapsed');

        // If preference is set to true, collapse the content
        if (roleProgressionPref === 'true') {
            elements.roleProgressionContent.classList.add('collapsed');
        }
    }

    // Initialize role progression visualization with a placeholder
    if (elements.roleProgressionContent) {
        console.log('Initializing role progression content with placeholder');
        elements.roleProgressionContent.innerHTML = '<div class="role-progression-placeholder">Career progression visualization loading...</div>';
    }

    // Initialize organization chart with a placeholder
    if (elements.orgChart) {
        console.log('Initializing org chart content with placeholder');
        elements.orgChart.innerHTML = '<div class="org-chart-placeholder">Organization chart loading...</div>';
    }

    // Expose key functions globally for use in other scripts
    window.updateOrgChart = updateOrgChart;
    window.generateOrgChartHtml = generateOrgChartHtml;
    window.updateRoleProgression = updateRoleProgression;
    window.generateRoleProgressionHtml = generateRoleProgressionHtml;

    // Add a direct update function for debugging
    window.forceUpdateOrgChart = function(role) {
        console.log('Force updating org chart with role:', role);
        if (role) {
            gameState.currentRole = role;
        }

        // Add applicant to completed roles if current role is junior_assistant
        if (gameState.currentRole === 'junior_assistant' &&
            (!gameState.completedRoles || !gameState.completedRoles.includes('applicant'))) {
            console.log('Adding applicant to completed roles');
            if (!gameState.completedRoles) {
                gameState.completedRoles = [];
            }
            gameState.completedRoles.push('applicant');
        }

        // Generate and update the chart
        const html = generateOrgChartHtml(gameState.currentRole, gameState.completedRoles || []);
        if (html) {
            console.log('Generated org chart HTML with role:', gameState.currentRole);

            // Update main org chart
            const mainOrgChart = document.getElementById('org-chart');
            if (mainOrgChart) {
                console.log('Updating main org chart');
                mainOrgChart.innerHTML = html;
            }

            // Update mobile org chart
            const mobileOrgChart = document.getElementById('mobile-org-chart');
            if (mobileOrgChart) {
                console.log('Updating mobile org chart');
                mobileOrgChart.innerHTML = html;
            }

            return true;
        }
        return false;
    };

    // Check if user is authenticated (not anonymous)
    const isAnonymousUser = document.body.classList.contains('anonymous-user');

    if (!isAnonymousUser) {
        // For authenticated users, continue the game from where they left off
        await continueGame();
    } else {
        // For anonymous users, start a new game
        await startGame();
    }

    // Add an additional check for pending tasks after the game starts
    // This helps recover from situations where the game might get stuck
    setTimeout(() => {
        console.log('DEBUG - Additional check for pending tasks');
        if (gameState.nextTaskPending) {
            console.log('DEBUG - Found pending next task in additional check, fetching it');
            fetchNextTask();
        }
    }, 10000); // Check after 10 seconds
}

// Continue the game from where the user left off
async function continueGame() {
    console.log('Continuing game...');
    showLoading();

    try {
        console.log('Fetching current game state...');
        const response = await fetch(API_ENDPOINTS.GET_GAME_STATE);
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('Game state data received:', data);

        if (data.status === 'success') {
            // Update game state
            gameState.messages = data.messages || [];
            gameState.currentRole = data.current_role || 'applicant';
            gameState.performanceScore = data.performance_score || 0;
            gameState.characters = data.characters || {};
            gameState.challengesCompleted = data.total_challenges_completed || 0;
            gameState.roleChallengesCompleted = data.role_challenges_completed || 0;
            gameState.gameCompleted = data.game_completed || false;
            gameState.currentManager = data.current_manager || 'hr';
            gameState.currentTask = data.current_task || 'cover_letter';
            gameState.orgChartHtml = data.org_chart_html || null;
            gameState.completedRoles = data.completed_roles || [];
            gameState.roleProgressionInfo = data.role_progression || {};
            gameState.challengesRequired = data.challenges_required || 3;
            gameState.roleProgressionHtml = data.role_progression_html || null;
            gameState.firstTaskPending = data.first_task_pending || false;
            gameState.nextTaskPending = data.next_task_pending || false;

            console.log('Game state loaded with messages:', gameState.messages.length);
            console.log('First task pending:', gameState.firstTaskPending);
            console.log('Next task pending:', gameState.nextTaskPending);

            // Update UI with all persisted messages
            updateUI();

            // CRITICAL BUGFIX: Completely revised handling of first task to prevent duplicates
            if (gameState.firstTaskPending) {
                console.log('First task is pending, checking if it already exists in UI');

                // First check if the task already exists in the UI
                const existingTaskElements = Array.from(elements.messagesContainer.querySelectorAll('.message')).filter(el =>
                    el.dataset.taskId === "cover_letter" && el.dataset.sender === "hr"
                );

                if (existingTaskElements.length > 0) {
                    console.log('CRITICAL BUGFIX - First task already exists in UI, not fetching again');
                    gameState.firstTaskPending = false;
                } else {
                    // Then check if we already have the first task in our messages
                    const existingFirstTask = gameState.messages.find(msg =>
                        msg.is_challenge && msg.task_id === "cover_letter" && msg.sender === "hr"
                    );

                    if (existingFirstTask) {
                        console.log('BUGFIX - First task already exists in messages, not fetching again:', existingFirstTask);
                        // Just update the UI with the existing message
                        gameState.firstTaskPending = false;
                    } else {
                        console.log('First task not found in messages or UI, will fetch after delay');
                        // Use a longer delay to ensure the game state is fully loaded
                        // BUGFIX: Use a single setTimeout to prevent multiple calls
                        if (window.firstTaskTimeout) {
                            clearTimeout(window.firstTaskTimeout);
                        }
                        window.firstTaskTimeout = setTimeout(() => {
                            // Double-check that the task still doesn't exist before fetching
                            const taskElements = Array.from(elements.messagesContainer.querySelectorAll('.message')).filter(el =>
                                el.dataset.taskId === "cover_letter" && el.dataset.sender === "hr"
                            );

                            if (taskElements.length === 0) {
                                fetchFirstTask();
                            } else {
                                console.log('CRITICAL BUGFIX - First task appeared in UI during timeout, not fetching');
                                gameState.firstTaskPending = false;
                            }
                        }, 2000);
                    }
                }
            }

            // Check if next task is pending and fetch it after a delay
            if (gameState.nextTaskPending) {
                console.log('Next task is pending, will fetch after delay');
                setTimeout(fetchNextTask, 2500); // Use a different delay to avoid conflicts
            }

            // Hide game complete modal if visible
            elements.gameCompleteModal.classList.add('hidden');
        } else {
            console.error('Failed to continue game:', data.message);
            alert('Failed to continue game. Please try again.');
        }
    } catch (error) {
        console.error('Error continuing game:', error);
        alert('Error connecting to the server. Please check your connection and try again.');
    } finally {
        hideLoading();
    }
}

// Start or restart the game
async function startGame() {
    console.log('Starting game...');
    showLoading();

    // BUGFIX: Thoroughly clear any existing messages in the UI
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
        console.log('Cleared message container in UI');
    }

    // BUGFIX: Thoroughly reset the game state
    gameState.messages = [];
    gameState.currentRole = 'applicant';
    gameState.performanceScore = 0;
    gameState.challengesCompleted = 0;
    gameState.roleChallengesCompleted = 0;
    gameState.gameCompleted = false;
    gameState.currentManager = 'hr';
    gameState.currentTask = 'cover_letter';
    gameState.completedRoles = [];
    gameState.firstTaskPending = true;
    gameState.nextTaskPending = false;
    gameState.taskCompleted = false;

    console.log('BUGFIX - Thoroughly reset gameState');

    // Check if we should show the login prompt for anonymous users
    const isAnonymousUser = document.body.classList.contains('anonymous-user');
    const continueAsGuest = localStorage.getItem('continue_as_guest') === 'true';

    if (isAnonymousUser && !continueAsGuest) {
        // Show login prompt after a short delay
        setTimeout(() => {
            const loginPromptModal = document.getElementById('login-prompt-modal');
            if (loginPromptModal) {
                loginPromptModal.classList.remove('hidden');
            }
        }, 2000);
    }

    try {
        console.log('Fetching game state...');
        // Add cache-busting parameter to ensure we get a fresh response
        const timestamp = new Date().getTime();
        const response = await fetch(`${API_ENDPOINTS.START_GAME}?t=${timestamp}`, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
        console.log('Response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();
        console.log('Game state data received:', data);

        if (data.status === 'success') {
            console.log('Game state data received:', data);
            console.log('Role progression HTML in response:', data.role_progression_html ? 'Present' : 'Missing');
            if (data.role_progression_html) {
                console.log('Role progression HTML content:', data.role_progression_html.substring(0, 100) + '...');
            }
            console.log('Org chart HTML in response:', data.org_chart_html ? 'Present' : 'Missing');

            // Update game state
            gameState.messages = data.messages || [];
            gameState.currentRole = data.current_role || 'applicant';
            gameState.performanceScore = data.performance_score || 0;
            gameState.characters = data.characters || {};
            gameState.challengesCompleted = data.total_challenges_completed || 0;

            // CRITICAL FIX: Always initialize roleChallengesCompleted to 0 for new games
            // This prevents the first task from being counted twice
            if (data.role_challenges_completed !== undefined) {
                // Only use server value if it's valid (0 or positive integer)
                if (Number.isInteger(data.role_challenges_completed) && data.role_challenges_completed >= 0) {
                    gameState.roleChallengesCompleted = data.role_challenges_completed;
                    console.log('BUGFIX - Initialized roleChallengesCompleted from server data:', gameState.roleChallengesCompleted);

                    // CRITICAL FIX: Ensure roleChallengesCompleted never exceeds challenges required
                    const challengesRequired = data.challenges_required || 3;
                    if (gameState.roleChallengesCompleted > challengesRequired) {
                        console.log('CRITICAL FIX - roleChallengesCompleted exceeds challengesRequired, capping at:', challengesRequired);
                        gameState.roleChallengesCompleted = challengesRequired;
                    }
                } else {
                    // Invalid value from server, reset to 0
                    gameState.roleChallengesCompleted = 0;
                    console.log('CRITICAL FIX - Server provided invalid roleChallengesCompleted, resetting to 0');
                }
            } else {
                // No value from server, set to 0
                gameState.roleChallengesCompleted = 0;
                console.log('BUGFIX - Server did not provide roleChallengesCompleted, defaulting to 0');
            }
            gameState.gameCompleted = data.game_completed || false;
            gameState.currentManager = data.current_manager || 'hr';
            gameState.currentTask = data.current_task || 'cover_letter';
            gameState.orgChartHtml = data.org_chart_html || null;
            gameState.completedRoles = data.completed_roles || [];
            gameState.roleProgressionInfo = data.role_progression || {};
            gameState.challengesRequired = data.challenges_required || 3;
            gameState.roleProgressionHtml = data.role_progression_html || null;
            gameState.firstTaskPending = data.first_task_pending || false;
            gameState.nextTaskPending = data.next_task_pending || false;
            gameState.taskCompleted = false; // Reset task completed flag on game start

            console.log('Game state updated with role progression HTML:', gameState.roleProgressionHtml ? 'Present' : 'Missing');
            console.log('Game state updated with org chart HTML:', gameState.orgChartHtml ? 'Present' : 'Missing');
            console.log('First task pending:', gameState.firstTaskPending);
            console.log('Next task pending:', gameState.nextTaskPending);

            console.log(`Game started with manager: ${gameState.currentManager}, task: ${gameState.currentTask}`);

            // BUGFIX: Update character info when game starts
            if (gameState.currentManager && gameState.characters && gameState.characters[gameState.currentManager]) {
                updateCharacterInfo(gameState.currentManager);
            }

            // CRITICAL BUGFIX: Completely revised handling of first task to prevent duplicates
            if (gameState.firstTaskPending) {
                console.log('First task is pending, checking if it already exists in UI');

                // First check if the task already exists in the UI
                const existingTaskElements = Array.from(elements.messagesContainer.querySelectorAll('.message')).filter(el =>
                    el.dataset.taskId === "cover_letter" && el.dataset.sender === "hr"
                );

                if (existingTaskElements.length > 0) {
                    console.log('CRITICAL BUGFIX - First task already exists in UI, not fetching again');
                    gameState.firstTaskPending = false;
                } else {
                    // Then check if we already have the first task in our messages
                    const existingFirstTask = gameState.messages.find(msg =>
                        msg.is_challenge && msg.task_id === "cover_letter" && msg.sender === "hr"
                    );

                    if (existingFirstTask) {
                        console.log('BUGFIX - First task already exists in messages, not fetching again:', existingFirstTask);
                        // Just update the UI with the existing message
                        gameState.firstTaskPending = false;
                    } else {
                        console.log('First task not found in messages or UI, will fetch after delay');
                        // Use a longer delay to ensure the game state is fully loaded
                        // BUGFIX: Use a single setTimeout to prevent multiple calls
                        if (window.firstTaskTimeout) {
                            clearTimeout(window.firstTaskTimeout);
                        }
                        window.firstTaskTimeout = setTimeout(() => {
                            // Double-check that the task still doesn't exist before fetching
                            const taskElements = Array.from(elements.messagesContainer.querySelectorAll('.message')).filter(el =>
                                el.dataset.taskId === "cover_letter" && el.dataset.sender === "hr"
                            );

                            if (taskElements.length === 0) {
                                fetchFirstTask();
                            } else {
                                console.log('CRITICAL BUGFIX - First task appeared in UI during timeout, not fetching');
                                gameState.firstTaskPending = false;
                            }
                        }, 3000);
                    }
                }
            }

            // Check if next task is pending and fetch it after a delay
            if (gameState.nextTaskPending) {
                console.log('Next task is pending, will fetch after delay');
                setTimeout(fetchNextTask, 3000); // Use a different delay to avoid conflicts
            }

            // If role progression HTML is missing, try to fetch it directly
            if (!gameState.roleProgressionHtml) {
                console.log('Role progression HTML missing, fetching directly...');
                fetch(API_ENDPOINTS.GET_GAME_STATE)
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success' && data.role_progression_html) {
                            console.log('Received role progression HTML from server');
                            gameState.roleProgressionHtml = data.role_progression_html;
                            // Update UI after getting the HTML
                            updateUI();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching role progression HTML:', error);
                        // Update UI anyway
                        updateUI();
                    });
            } else {
                // Update UI
                updateUI();
            }

            // Hide game complete modal if visible
            elements.gameCompleteModal.classList.add('hidden');
        } else {
            console.error('Failed to start game:', data.message);
            alert('Failed to start game. Please try again.');
        }
    } catch (error) {
        console.error('Error starting game:', error);
        alert('Error connecting to the server. Please check your connection and try again.');
    } finally {
        hideLoading();
    }
}

// Update the UI based on current game state
function updateUI() {
    // Update role and score displays
    elements.currentRole.textContent = capitalizeFirstLetter(gameState.currentRole.replace('_', ' '));
    elements.roleDisplay.textContent = capitalizeFirstLetter(gameState.currentRole.replace('_', ' '));
    elements.performanceScore.textContent = gameState.performanceScore;
    elements.challengesCompleted.textContent = gameState.challengesCompleted;

    // BUGFIX: Removed the manual next task button and all its logic
    // Instead, we'll rely on automatic task progression

    // Remove any existing manual next task button
    const manualNextTaskButton = document.getElementById('manual-next-task-button');
    if (manualNextTaskButton) {
        console.log('BUGFIX - Removing manual next task button');
        manualNextTaskButton.remove();
    }

    // If a task was just completed or next task is pending, automatically fetch the next task
    if (gameState.nextTaskPending || gameState.taskCompleted) {
        console.log('DEBUG - Task completed or next task pending. Setting up automatic task progression.');

        // Reset the taskCompleted flag
        gameState.taskCompleted = false;
    }

    // Update progress bar based on role challenges completed
    // This shows progress toward the next promotion
    // IMPORTANT: Each role has exactly 3 tasks, matching Sarah Chen's implementation
    // Users must complete all 3 tasks for a role before advancing to the next role
    let progressPercentage = 0;
    let requiredChallenges = gameState.challengesRequired || 3; // Default to 3 tasks per role

    // BUGFIX: Ensure roleChallengesCompleted is a valid number
    if (typeof gameState.roleChallengesCompleted !== 'number' || isNaN(gameState.roleChallengesCompleted)) {
        console.log('BUGFIX - roleChallengesCompleted is not a valid number, resetting to 0');
        gameState.roleChallengesCompleted = 0;
    }

    // BUGFIX: Ensure roleChallengesCompleted doesn't exceed the required challenges
    if (gameState.roleChallengesCompleted > requiredChallenges) {
        console.log('BUGFIX - roleChallengesCompleted exceeds requiredChallenges, capping at', requiredChallenges);
        gameState.roleChallengesCompleted = requiredChallenges;
    }

    // CRITICAL FIX: Ensure roleChallengesCompleted is valid before calculating progress
    let displayValue = gameState.roleChallengesCompleted;

    // Ensure it's a valid number
    if (typeof displayValue !== 'number' || isNaN(displayValue)) {
        console.log('CRITICAL FIX - Invalid roleChallengesCompleted value, resetting to 0 for progress calculation');
        displayValue = 0;
    }

    // Ensure it doesn't exceed required challenges
    if (displayValue > requiredChallenges) {
        console.log('CRITICAL FIX - roleChallengesCompleted exceeds requiredChallenges, capping at', requiredChallenges, 'for progress calculation');
        displayValue = requiredChallenges;
    }

    // Calculate progress percentage using the validated value
    progressPercentage = Math.min((displayValue / requiredChallenges) * 100, 100);

    // Update role progress display using the same validated value
    if (elements.roleProgress) {

        elements.roleProgress.textContent = `${displayValue}/${requiredChallenges}`;
        console.log('BUGFIX - Updated role progress display in updateUI to:', elements.roleProgress.textContent);
    }
    elements.progressBar.style.width = `${progressPercentage}%`;

    // Clear messages container
    elements.messagesContainer.innerHTML = '';

    // Prepare messages for display
    // We want to show all user messages, AI responses, and manager feedback
    // For tasks, we only want to show the current task to avoid confusion
    const displayedTaskIds = new Set();
    const messagesToDisplay = [];

    // First, identify the current task_id
    const currentTaskId = gameState.currentTask;
    console.log('Current task ID:', currentTaskId);
    console.log('Total messages to process:', gameState.messages.length);

    // BUGFIX: Improved message processing to ensure task history is preserved
    // First, create a map to track the most recent message for each task_id
    const taskMessages = new Map();

    // Process messages in chronological order to build our task message map
    // This ensures we have the most recent version of each task
    [...gameState.messages].forEach(message => {
        // Track task messages by their task_id
        if (message.is_challenge && message.task_id) {
            taskMessages.set(message.task_id, message);
        }
    });

    console.log('Found task messages for task IDs:', Array.from(taskMessages.keys()));

    // BUGFIX: Enhanced message processing to ensure ALL tasks are preserved without duplicates
    // First, deduplicate the messages array to ensure we don't have duplicate messages in gameState
    const uniqueMessages = [];
    const seenMessageIds = new Set();
    const seenTaskMessages = new Map(); // Map of task_id -> message

    // First pass: identify unique messages and the most recent version of each task
    gameState.messages.forEach(message => {
        // Skip if we've already seen this exact message ID
        if (seenMessageIds.has(message.id)) {
            console.log('BUGFIX - Skipping duplicate message with ID:', message.id);
            return;
        }

        // For task messages, keep track of the most recent version of each task
        if (message.is_challenge && message.task_id) {
            // If we haven't seen this task before, or this is a newer version, update the map
            if (!seenTaskMessages.has(message.task_id) ||
                new Date(message.timestamp) > new Date(seenTaskMessages.get(message.task_id).timestamp)) {
                seenTaskMessages.set(message.task_id, message);
            }
        } else {
            // For non-task messages, add them to the unique messages array
            seenMessageIds.add(message.id);
            uniqueMessages.push(message);
        }
    });

    // Add the most recent version of each task to the unique messages array
    seenTaskMessages.forEach(message => {
        seenMessageIds.add(message.id);
        uniqueMessages.push(message);
    });

    // Sort the unique messages by timestamp to preserve conversation flow
    uniqueMessages.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    console.log('BUGFIX - Deduplicated messages array:', uniqueMessages.length, 'messages (from', gameState.messages.length, 'original messages)');

    // Now process the deduplicated messages
    uniqueMessages.forEach(message => {
        // Always include welcome messages (from HR at the beginning)
        const isWelcomeMessage = message.sender === "hr" &&
                                message.text &&
                                message.text.includes("Welcome to") &&
                                message.text.includes("We're excited to have you join our application process");

        if (isWelcomeMessage) {
            console.log('Including welcome message:', message.id);
            messagesToDisplay.push(message);
            return;
        }

        // Include all task messages (challenges) to preserve conversation history
        if (message.is_challenge && message.task_id) {
            console.log('Including task message for task_id:', message.task_id);
            // Mark this task_id as seen
            displayedTaskIds.add(message.task_id);
            // Add the message to our display list
            messagesToDisplay.push(message);
            return;
        }

        // Add all other messages (user messages, AI responses, etc.)
        messagesToDisplay.push(message);
    });

    console.log('Messages to display after filtering:', messagesToDisplay.length);

    // CRITICAL BUGFIX: Improved message display to prevent duplicates
    // Create a map to track which messages we've already displayed
    const displayedMessages = new Map();

    // First pass: identify which messages we want to display
    const messagesToActuallyDisplay = [];
    messagesToDisplay.forEach(message => {
        // Skip system messages, but always show task messages (is_challenge), promotion messages, welcome messages, and resent messages
        const isWelcomeMessage = message.sender === "hr" &&
                                message.text &&
                                message.text.includes("Welcome to") &&
                                message.text.includes("We're excited to have you join our application process");

        if (message.sender !== 'system' || message.is_challenge || message.is_resent || message.is_promotion || isWelcomeMessage) {
            // Create a unique key for this message based on content and sender
            let messageKey;

            if (message.is_challenge && message.task_id) {
                // For task messages, use task_id and sender as the key
                messageKey = `task_${message.task_id}_${message.sender}`;
            } else if (isWelcomeMessage) {
                // For welcome messages, use a special key
                messageKey = 'welcome_message';
            } else {
                // For other messages, use ID as the key
                messageKey = message.id;
            }

            // Only add this message if we haven't seen it before
            if (!displayedMessages.has(messageKey)) {
                displayedMessages.set(messageKey, message);
                messagesToActuallyDisplay.push(message);
                console.log('BUGFIX - Adding message to display list:', messageKey);
            } else {
                console.log('BUGFIX - Skipping duplicate message:', messageKey);
            }
        }
    });

    console.log('BUGFIX - Messages to actually display after deduplication:', messagesToActuallyDisplay.length);

    // Now display the deduplicated messages
    messagesToActuallyDisplay.forEach(message => {
        console.log('Adding message to UI:', message.id, message.sender, message.text.substring(0, 30) + '...');
        addMessageToUI(message);
    });

    // Scroll to bottom of messages
    elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;

    // Reset input area
    setStep('prompt');
    elements.promptInput.value = '';

    // BUGFIX: Force a refresh of the game state from the server to ensure we have the latest data
    // This is especially important after a promotion
    console.log('BUGFIX - Fetching latest game state to ensure sidebar is up-to-date');
    fetch(API_ENDPOINTS.GET_GAME_STATE)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update local game state with server data
                if (data.current_role) gameState.currentRole = data.current_role;
                if (data.current_manager) gameState.currentManager = data.current_manager;
                if (data.current_task) gameState.currentTask = data.current_task;

                // CRITICAL FIX: Properly handle role challenges completed count from server
                if (data.role_challenges_completed !== undefined) {
                    // Only use server value if it's valid (0 or positive integer)
                    if (Number.isInteger(data.role_challenges_completed) && data.role_challenges_completed >= 0) {
                        gameState.roleChallengesCompleted = data.role_challenges_completed;
                        console.log('BUGFIX - Updated roleChallengesCompleted from server:', gameState.roleChallengesCompleted);

                        // CRITICAL FIX: Ensure roleChallengesCompleted never exceeds challenges required
                        const challengesRequired = data.challenges_required || gameState.challengesRequired || 3;
                        if (gameState.roleChallengesCompleted > challengesRequired) {
                            console.log('CRITICAL FIX - roleChallengesCompleted exceeds challengesRequired, capping at:', challengesRequired);
                            gameState.roleChallengesCompleted = challengesRequired;
                        }

                        // Force update the role progress display immediately
                        if (elements.roleProgress) {
                            let requiredChallenges = gameState.challengesRequired || 3;
                            elements.roleProgress.textContent = `${gameState.roleChallengesCompleted}/${requiredChallenges}`;
                            console.log('BUGFIX - Immediately updated role progress display to:', elements.roleProgress.textContent);
                        }
                    } else {
                        // Invalid value from server, don't update
                        console.log('CRITICAL FIX - Server provided invalid roleChallengesCompleted:', data.role_challenges_completed);
                    }
                }

                console.log('BUGFIX - Updated game state from server:', {
                    currentRole: gameState.currentRole,
                    currentManager: gameState.currentManager,
                    currentTask: gameState.currentTask,
                    roleChallengesCompleted: gameState.roleChallengesCompleted
                });

                // Now update the UI with the latest data
                updateSidebar();
            }
        })
        .catch(error => {
            console.error('Error fetching latest game state:', error);
            // Fall back to using the current game state
            updateSidebar();
        });

    // Helper function to update the sidebar with the current game state
    function updateSidebar() {
        // Update current manager and task if elements exist
        if (elements.currentManager) {
            elements.currentManager.textContent = gameState.currentManager ?
                gameState.characters[gameState.currentManager]?.name || gameState.currentManager : 'None';
            console.log('BUGFIX - Updated current manager display to:', elements.currentManager.textContent);
        }

        if (elements.currentTask) {
            // BUGFIX: Make sure we properly format the task ID by replacing all underscores with spaces
            if (gameState.currentTask) {
                let formattedTaskName = gameState.currentTask;
                // Replace all underscores with spaces, not just the first one
                formattedTaskName = formattedTaskName.replace(/_/g, ' ');
                // Capitalize each word
                formattedTaskName = formattedTaskName.split(' ')
                    .map(word => capitalizeFirstLetter(word))
                    .join(' ');

                elements.currentTask.textContent = formattedTaskName;
            } else {
                elements.currentTask.textContent = 'None';
            }
            console.log('BUGFIX - Updated current task display to:', elements.currentTask.textContent);
        }

        // Update role progress display again with the latest data
        if (elements.roleProgress) {
            let requiredChallenges = gameState.challengesRequired || 3;

            // BUGFIX: Ensure roleChallengesCompleted is a valid number
            if (typeof gameState.roleChallengesCompleted !== 'number' || isNaN(gameState.roleChallengesCompleted)) {
                console.log('BUGFIX - roleChallengesCompleted is not a valid number in updateSidebar, resetting to 0');
                gameState.roleChallengesCompleted = 0;
            }

            // BUGFIX: Ensure roleChallengesCompleted doesn't exceed the required challenges
            if (gameState.roleChallengesCompleted > requiredChallenges) {
                console.log('BUGFIX - roleChallengesCompleted exceeds requiredChallenges in updateSidebar, capping at', requiredChallenges);
                gameState.roleChallengesCompleted = requiredChallenges;
            }

            elements.roleProgress.textContent = `${gameState.roleChallengesCompleted}/${requiredChallenges}`;
            console.log('BUGFIX - Updated role progress display in updateSidebar to:', elements.roleProgress.textContent);
        }

        // BUGFIX: Update character info in the sidebar when manager changes
        if (gameState.currentManager && gameState.characters && gameState.characters[gameState.currentManager]) {
            updateCharacterInfo(gameState.currentManager);
            console.log('BUGFIX - Updated character info for manager:', gameState.currentManager);
        }
    }

    // Update organization chart
    console.log('Calling updateOrgChart from updateUI');
    updateOrgChart();

    // Update role progression visualization
    console.log('Calling updateRoleProgression from updateUI');
    updateRoleProgression();

    // Reinitialize chart zoom functionality
    if (window.reinitializeChartZoom) {
        console.log('Reinitializing chart zoom functionality from updateUI');
        window.reinitializeChartZoom();
    }
}

// Update the role progression visualization
function updateRoleProgression() {
    console.log('updateRoleProgression called');
    console.log('elements:', elements);
    console.log('roleProgressionContent:', elements.roleProgressionContent);

    if (!elements.roleProgressionContent) {
        console.error('Role progression content element not found');
        // Try to find the element again or create it if it doesn't exist
        const roleProgressionWrapper = document.getElementById('role-progression-wrapper');
        if (roleProgressionWrapper) {
            console.log('Found role-progression-wrapper, checking for content element');
            let contentElement = document.getElementById('role-progression-content');
            if (!contentElement) {
                console.log('Creating role-progression-content element');
                contentElement = document.createElement('div');
                contentElement.id = 'role-progression-content';
                contentElement.className = 'role-progression-content';
                roleProgressionWrapper.appendChild(contentElement);
                elements.roleProgressionContent = contentElement;
            } else {
                console.log('Found existing role-progression-content element');
                elements.roleProgressionContent = contentElement;
            }
        } else {
            console.error('Role progression wrapper element not found');
            return;
        }
    }

    console.log('Updating role progression visualization');
    console.log('gameState:', gameState);
    console.log('Role progression HTML:', gameState.roleProgressionHtml ? 'Present' : 'Missing');
    if (gameState.roleProgressionHtml) {
        console.log('HTML content:', gameState.roleProgressionHtml.substring(0, 100) + '...');
    }

    // Check if we have the role progression HTML from the server
    if (gameState.roleProgressionHtml) {
        console.log('Setting role progression HTML');
        elements.roleProgressionContent.innerHTML = gameState.roleProgressionHtml;
    } else {
        console.log('Setting placeholder for role progression');
        elements.roleProgressionContent.innerHTML = '<div class="role-progression-placeholder">Career progression visualization loading...</div>';

        // Try to fetch the role progression HTML from the server
        console.log('Attempting to fetch role progression HTML from server');
        fetch(API_ENDPOINTS.GET_GAME_STATE)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.role_progression_html) {
                    console.log('Received role progression HTML from server');
                    gameState.roleProgressionHtml = data.role_progression_html;
                    elements.roleProgressionContent.innerHTML = gameState.roleProgressionHtml;
                } else {
                    // If we still don't have the HTML, try to generate it manually
                    console.log('No role progression HTML in response, generating manually');
                    const html = generateRoleProgressionHtml(gameState.currentRole, gameState.completedRoles || []);
                    if (html) {
                        console.log('Generated role progression HTML manually');
                        gameState.roleProgressionHtml = html;
                        elements.roleProgressionContent.innerHTML = html;
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching role progression HTML:', error);
                // Try to generate it manually as a fallback
                const html = generateRoleProgressionHtml(gameState.currentRole, gameState.completedRoles || []);
                if (html) {
                    console.log('Generated role progression HTML manually after fetch error');
                    gameState.roleProgressionHtml = html;
                    elements.roleProgressionContent.innerHTML = html;
                }
            });
    }
}

// Toggle role progression visibility
function toggleRoleProgression() {
    if (!elements.roleProgressionContent) return;

    // Toggle collapsed class (note: we're using collapsed now, not expanded)
    elements.roleProgressionContent.classList.toggle('collapsed');

    // Store user preference in localStorage
    const isCollapsed = elements.roleProgressionContent.classList.contains('collapsed');
    localStorage.setItem('roleProgressionCollapsed', isCollapsed);
}

// Generate organization chart HTML on the client side as a fallback
function generateOrgChartHtml(currentRole, completedRoles) {
    console.log('Generating org chart HTML for', currentRole, 'with completed roles:', completedRoles);

    // Define the organizational structure for the chart
    const orgStructure = [
        // Executive Level
        {
            "level": "Executive",
            "roles": ["shareholders", "ceo", "coo"]
        },
        // VP Level
        {
            "level": "Vice Presidents",
            "roles": ["vp_marketing", "vp_operations", "vp_finance", "hr_director"]
        },
        // Manager Level
        {
            "level": "Managers",
            "roles": [
                "advertising_manager", "sales_manager",  // Marketing
                "service_manager", "production_manager", "facilities_manager",  // Operations
                "accounts_receivable_manager", "accounts_payable_manager",  // Finance
                "hr_manager"  // HR Department
            ]
        },
        // Associate Level
        {
            "level": "Associates",
            "roles": [
                "sales_associate", "marketing_associate",  // Marketing
                "service_associate", "production_associate", "facilities_associate",  // Operations
                "accounts_receivable_associate", "accounts_payable_associate",  // Finance
                "hr_coordinator"  // HR Department
            ]
        },
        // Entry Level
        {
            "level": "Entry",
            "roles": ["junior_assistant", "applicant"]
        }
    ];

    // Role titles for display
    const roleTitles = {
        "shareholders": "Shareholders",
        "ceo": "Chief Executive Officer",
        "coo": "Chief Operating Officer",
        "vp_marketing": "VP Marketing",
        "vp_operations": "VP Operations",
        "vp_finance": "VP Finance",
        "hr_director": "HR Director",
        "advertising_manager": "Advertising Manager",
        "sales_manager": "Sales Manager",
        "service_manager": "Service Manager",
        "production_manager": "Production Manager",
        "facilities_manager": "Facilities Manager",
        "accounts_receivable_manager": "AR Manager",
        "accounts_payable_manager": "AP Manager",
        "hr_manager": "HR Manager",
        "sales_associate": "Sales Associate",
        "marketing_associate": "Marketing Associate",
        "service_associate": "Service Associate",
        "production_associate": "Production Associate",
        "facilities_associate": "Facilities Associate",
        "accounts_receivable_associate": "AR Associate",
        "accounts_payable_associate": "AP Associate",
        "hr_coordinator": "HR Coordinator",
        "junior_assistant": "Junior Assistant",
        "applicant": "Applicant"
    };

    // Get status indicator based on role status
    function getStatusIndicator(status) {
        if (status === "completed") {
            return "✓";
        } else if (status === "current") {
            return "➤";
        } else {
            return "○";
        }
    }

    // Start building the HTML
    let html = '<div class="org-chart-content">';

    // Add each level
    for (const level of orgStructure) {
        html += `<div class="org-level"><div class="level-title">${level["level"]}</div><div class="level-roles">`;

        // Add roles for this level
        for (const role of level["roles"]) {
            // Determine role status
            let status;
            if (role === currentRole) {
                status = "current";
                console.log(`Marking role ${role} as current`);
            } else if (completedRoles && completedRoles.includes(role)) {
                status = "completed";
            } else {
                status = "future";
            }

            // Get role title
            const title = roleTitles[role] || role.replace("_", " ").replace(/\b\w/g, c => c.toUpperCase());

            // Add role node with enhanced styling for current role
            if (status === "current") {
                html += `<div class="org-node ${status}" title="${title}" data-position="${role}">`;
                html += `<div class="node-indicator">${getStatusIndicator(status)}</div>`;
                html += `<div class="node-title"><strong>${title}</strong></div>`;
                html += `<div class="current-marker">YOU ARE HERE</div>`;
                html += '</div>';
                console.log(`Added current role marker for ${role} with title ${title}`);
            } else {
                html += `<div class="org-node ${status}" title="${title}" data-position="${role}">`;
                html += `<div class="node-indicator">${getStatusIndicator(status)}</div>`;
                html += `<div class="node-title">${title}</div>`;
                html += '</div>';
            }
        }

        html += '</div></div>';
    }

    html += '</div>';

    // Log the generated HTML for debugging
    console.log('Generated org chart HTML with current role:', currentRole);
    console.log('HTML excerpt:', html.substring(0, 200) + '...');

    // Remove any HTML comments that might contain "system undefined"
    html = html.replace(/<!--[\s\S]*?-->/g, '');

    return html;
}

// Generate role progression HTML on the client side as a fallback
function generateRoleProgressionHtml(currentRole, completedRoles) {
    console.log('Generating role progression HTML for', currentRole, 'with completed roles:', completedRoles);

    // Define the role progression path organized by department
    const rolePath = [
        // Entry Level
        "applicant", "junior_assistant",
        // Marketing Department
        "sales_associate", "marketing_associate",
        "sales_manager", "advertising_manager", "vp_marketing",
        // Operations Department
        "service_associate", "production_associate", "facilities_associate",
        "service_manager", "production_manager", "facilities_manager", "vp_operations",
        // Finance Department
        "accounts_receivable_associate", "accounts_payable_associate",
        "accounts_receivable_manager", "accounts_payable_manager", "vp_finance",
        // HR Department
        "hr_coordinator", "hr_manager", "hr_director",
        // Executive Level
        "coo", "ceo", "shareholders"
    ];

    // Group roles by department for visualization
    const departments = {
        "Entry": ["applicant", "junior_assistant"],
        "Marketing": ["sales_associate", "marketing_associate", "sales_manager", "advertising_manager", "vp_marketing"],
        "Operations": ["service_associate", "production_associate", "facilities_associate", "service_manager", "production_manager", "facilities_manager", "vp_operations"],
        "Finance": ["accounts_receivable_associate", "accounts_payable_associate", "accounts_receivable_manager", "accounts_payable_manager", "vp_finance"],
        "HR": ["hr_coordinator", "hr_manager", "hr_director"],
        "Executive": ["coo", "ceo", "shareholders"]
    };

    // Find the current department
    let currentDepartment = null;
    for (const [dept, roles] of Object.entries(departments)) {
        if (roles.includes(currentRole)) {
            currentDepartment = dept;
            break;
        }
    }

    // Generate HTML for the visualization
    let html = '<div class="role-progression">';
    html += '<h3>Career Progression</h3>';

    // Add department headers
    html += '<div class="department-headers">';
    for (const dept of Object.keys(departments)) {
        const activeClass = dept === currentDepartment ? 'active-department' : '';
        html += `<div class="department-header ${activeClass}">${dept}</div>`;
    }
    html += '</div>';

    // Add role path
    html += '<div class="role-path">';

    for (let i = 0; i < rolePath.length; i++) {
        const role = rolePath[i];
        // Determine the status of this role
        let status;
        if (role === currentRole) {
            status = 'current';
        } else if (completedRoles.includes(role)) {
            status = 'completed';
        } else {
            status = 'future';
        }

        // Add the role node
        const roleTitle = role.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase());
        html += `<div class="role-node ${status}" title="${roleTitle}">${i+1}</div>`;

        // Add connector if not the last role
        if (i < rolePath.length - 1) {
            const connectorClass = completedRoles.includes(role) ? 'completed' : 'future';
            html += `<div class="role-connector ${connectorClass}"></div>`;
        }
    }

    html += '</div>';
    html += `<div class="current-role">Current Role: <strong>${currentRole.replace(/_/g, ' ').replace(/\b\w/g, c => c.toUpperCase())}</strong></div>`;

    // Add progress indicator
    const currentIndex = rolePath.indexOf(currentRole) !== -1 ? rolePath.indexOf(currentRole) : 0;
    const progressPercent = Math.floor((currentIndex / rolePath.length) * 100);
    html += `<div class="career-progress">Career Progress: <div class="progress-bar"><div class="progress-fill" style="width: ${progressPercent}%"></div></div> ${progressPercent}%</div>`;

    html += '</div>';

    return html;
}

// Debug functions removed

// Flag to prevent multiple simultaneous calls to fetchFirstTask
let fetchingFirstTask = false;

// Fetch the first task after a delay
async function fetchFirstTask() {
    // BUGFIX: Prevent multiple simultaneous calls to fetchFirstTask
    if (fetchingFirstTask) {
        console.log('BUGFIX - Already fetching first task, ignoring duplicate call');
        return;
    }

    // Set the flag to indicate we're fetching the first task
    fetchingFirstTask = true;

    console.log('Fetching first task...');
    try {
        // CRITICAL BUGFIX: Check if we already have this task in the UI
        // This is more reliable than checking gameState.messages
        const existingTaskElements = Array.from(elements.messagesContainer.querySelectorAll('.message')).filter(el =>
            el.dataset.taskId === "cover_letter" && el.dataset.sender === "hr"
        );

        if (existingTaskElements.length > 0) {
            console.log('CRITICAL BUGFIX - First task already exists in UI, not fetching again');

            // Update game state
            gameState.firstTaskPending = false;

            // Reset the flag
            fetchingFirstTask = false;
            return;
        }

        // Also check if we already have this task in our messages
        const existingTaskMessage = gameState.messages.find(msg =>
            msg.is_challenge && msg.task_id === "cover_letter" && msg.sender === "hr"
        );

        if (existingTaskMessage) {
            console.log('BUGFIX - First task already exists in messages, not fetching again:', existingTaskMessage);

            // Clear the messages container first to prevent duplicates
            elements.messagesContainer.innerHTML = '';

            // Update the UI with all messages
            updateUI();

            // Update game state
            gameState.firstTaskPending = false;

            // Reset the flag
            fetchingFirstTask = false;
            return;
        }

        const response = await fetch(API_ENDPOINTS.FETCH_FIRST_TASK);
        const data = await response.json();

        if (data.status === 'success') {
            console.log('First task fetched successfully:', data.message);

            // BUGFIX: Check if this message already exists in gameState.messages
            const isDuplicate = gameState.messages.some(msg =>
                msg.task_id === data.message.task_id &&
                msg.sender === data.message.sender
            );

            if (!isDuplicate) {
                // Add the message to the game state
                gameState.messages.push(data.message);

                // Clear the messages container first to prevent duplicates
                elements.messagesContainer.innerHTML = '';

                // Update the UI with all messages
                updateUI();
            } else {
                console.log('BUGFIX - Message already exists in gameState.messages, not adding duplicate');
            }

            // Update game state
            gameState.firstTaskPending = false;
        } else {
            console.error('Failed to fetch first task:', data.message);
        }
    } catch (error) {
        console.error('Error fetching first task:', error);
    } finally {
        // Reset the flag
        fetchingFirstTask = false;
    }
}

// Flag to prevent multiple simultaneous calls to fetchNextTask
let fetchingNextTask = false;

// Fetch the next task after a promotion or task completion
async function fetchNextTask() {
    // BUGFIX: Prevent multiple simultaneous calls to fetchNextTask
    if (fetchingNextTask) {
        console.log('BUGFIX - Already fetching next task, ignoring duplicate call');
        return;
    }

    // Set the flag to prevent multiple calls
    fetchingNextTask = true;

    console.log('Fetching next task after promotion or task completion...');
    console.log('DEBUG - nextTaskPending before fetch:', gameState.nextTaskPending);
    console.log('DEBUG - taskCompleted before fetch:', gameState.taskCompleted);
    console.log('DEBUG - currentRole:', gameState.currentRole);
    console.log('DEBUG - currentManager:', gameState.currentManager);
    console.log('DEBUG - currentTask:', gameState.currentTask);

    // Remove any manual next task button if it exists (cleanup)
    const manualNextTaskButton = document.getElementById('manual-next-task-button');
    if (manualNextTaskButton) {
        console.log('BUGFIX - Removing manual next task button');
        manualNextTaskButton.remove();
    }

    // BUGFIX: Only set nextTaskPending to true if we're not on the last task
    // This prevents the system from trying to fetch a non-existent next task
    const isLastTask = gameState.roleChallengesCompleted >= (gameState.challengesRequired - 1);

    if (isLastTask) {
        console.log('BUGFIX - On the last task of the role, not setting nextTaskPending');
        gameState.nextTaskPending = false;
    } else {
        console.log('BUGFIX - Not on the last task, setting nextTaskPending to true');
        gameState.nextTaskPending = true;
    }

    // Check if this is the first task after a promotion
    // If so, we only want to show the first task, not all tasks
    const isFirstTaskAfterPromotion = gameState.roleChallengesCompleted === 0 &&
                                     gameState.completedRoles &&
                                     gameState.completedRoles.length > 0;

    console.log('DEBUG - Is first task after promotion:', isFirstTaskAfterPromotion);
    console.log('DEBUG - Role challenges completed:', gameState.roleChallengesCompleted);
    console.log('DEBUG - Completed roles:', gameState.completedRoles);

    // Show loading indicator
    showLoading();

    try {
        console.log('DEBUG - Making fetch request to fetch_next_task');
        const response = await fetch(API_ENDPOINTS.FETCH_NEXT_TASK);
        console.log('DEBUG - Received response from fetch_next_task');

        const data = await response.json();
        console.log('DEBUG - Parsed JSON response:', data);

        if (data.status === 'success') {
            console.log('Next task fetched successfully:', data.message);

            // CRITICAL BUGFIX: DO NOT remove previous task messages
            // This preserves the conversation history
            console.log('CRITICAL BUGFIX - Preserving all task messages in conversation history');

            // Instead of removing messages, we'll just check if the current task message already exists
            const taskExists = gameState.messages.some(msg =>
                msg.is_challenge && msg.task_id === data.message.task_id && msg.sender === data.message.sender
            );

            // Check if this message already exists in gameState.messages
            if (taskExists) {
                console.log('BUGFIX - Message for task ' + data.message.task_id + ' already exists, not adding duplicate');
            } else {
                // Add the message to the game state
                gameState.messages.push(data.message);
                console.log('DEBUG - Added message to gameState.messages');
            }

            // Force a complete UI update to ensure only the current task is shown
            updateUI();
            console.log('DEBUG - Updated UI to show only the current task');

            // Scroll to the bottom of the messages container
            elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
            console.log('DEBUG - Scrolled to bottom of messages container');

            // Update game state
            gameState.nextTaskPending = false;
            console.log('DEBUG - Set nextTaskPending to false');

            // Reset taskCompleted flag
            gameState.taskCompleted = false;
            console.log('DEBUG - Reset taskCompleted flag to false');

            // Update current task and manager in game state if available in the message
            if (data.message.task_id) {
                gameState.currentTask = data.message.task_id;
                console.log('DEBUG - Updated currentTask to:', gameState.currentTask);

                // Update the current task display in the UI
                const currentTaskElement = document.getElementById('current-task');
                if (currentTaskElement) {
                    const formattedTaskName = data.message.task_id.replace(/_/g, ' ');
                    currentTaskElement.textContent = formattedTaskName;
                    console.log('DEBUG - Updated current task display to:', formattedTaskName);
                }
            }

            if (data.message.sender) {
                gameState.currentManager = data.message.sender;
                console.log('DEBUG - Updated currentManager to:', gameState.currentManager);

                // Update the current manager display in the UI
                const currentManagerElement = document.getElementById('current-manager');
                if (currentManagerElement) {
                    const managerName = gameState.characters && gameState.characters[data.message.sender] ?
                        gameState.characters[data.message.sender].name :
                        data.message.sender;
                    currentManagerElement.textContent = managerName;
                    console.log('DEBUG - Updated current manager display to:', managerName);
                }

                // Update character info in sidebar
                updateCharacterInfo(data.message.sender);
            }

            // Force UI update to ensure the message is displayed
            updateUI();
            console.log('DEBUG - Called updateUI to refresh the interface');

            // Update the organization chart to reflect any role changes
            updateOrgChart();
            console.log('DEBUG - Updated organization chart');

            // Update role progression visualization
            updateRoleProgression();
            console.log('DEBUG - Updated role progression visualization');

            // Remove any manual next task button that might be visible
            const manualNextTaskButton = document.getElementById('manual-next-task-button');
            if (manualNextTaskButton) {
                console.log('DEBUG - Removing manual next task button');
                manualNextTaskButton.remove();
            }

            // Highlight the new task message to draw attention to it
            setTimeout(() => {
                const messages = elements.messagesContainer.querySelectorAll('.message');
                const lastMessage = messages[messages.length - 1];
                if (lastMessage) {
                    lastMessage.classList.add('highlight-message');
                    setTimeout(() => {
                        lastMessage.classList.remove('highlight-message');
                    }, 2000);
                }
            }, 500);
        } else {
            console.error('Failed to fetch next task:', data.message);

            // Show error message to user
            showErrorMessage('Failed to fetch next task. Please try again.');

            // Manual next task button removed - tasks now progress automatically
            console.log('DEBUG - Attempting automatic recovery after error');
            // Try again after a short delay
            setTimeout(() => {
                if (gameState.nextTaskPending && !fetchingNextTask) {
                    console.log('DEBUG - Automatic retry after error');
                    try {
                        fetchNextTask();
                    } catch (retryError) {
                        console.error('Error in automatic retry:', retryError);
                    }
                }
            }, 3000); // 3 second delay
        }
    } catch (error) {
        console.error('Error fetching next task:', error);
        console.error('Error details:', error.stack);

        // Show error message to user
        showErrorMessage('Error fetching next task: ' + error.message);

        // Manual next task button removed - tasks now progress automatically
        console.log('DEBUG - Attempting automatic recovery after exception');
        // Try again after a short delay
        setTimeout(() => {
            if (gameState.nextTaskPending && !fetchingNextTask) {
                console.log('DEBUG - Automatic retry after exception');
                try {
                    fetchNextTask();
                } catch (retryError) {
                    console.error('Error in automatic retry after exception:', retryError);
                }
            }
        }, 3000); // 3 second delay
    } finally {
        // Hide loading indicator
        hideLoading();

        // Reset the flag to allow future calls
        fetchingNextTask = false;
        console.log('BUGFIX - Reset fetchingNextTask flag');
    }
}

// Update the organization chart based on current progress
function updateOrgChart() {
    console.log('updateOrgChart called');
    console.log('elements:', elements);
    console.log('orgChart:', elements.orgChart);

    // Update debug info if available
    if (document.getElementById('debug-panel')) {
        updateDebugInfo();
    }

    // Find both main and mobile org chart elements
    const mainOrgChart = document.getElementById('org-chart');
    const mobileOrgChart = document.getElementById('mobile-org-chart');

    // Update elements.orgChart if it's not set but exists in DOM
    if (!elements.orgChart && mainOrgChart) {
        elements.orgChart = mainOrgChart;
    }

    if (!elements.orgChart) {
        console.error('Org chart element not found');
        // Try to find the element again
        const orgChartContainer = document.querySelector('.org-chart-container');
        if (orgChartContainer) {
            console.log('Found org-chart-container, checking for org chart element');
            let chartElement = document.getElementById('org-chart');
            if (!chartElement) {
                console.log('Creating org-chart element');
                chartElement = document.createElement('div');
                chartElement.id = 'org-chart';
                chartElement.className = 'org-chart';
                orgChartContainer.appendChild(chartElement);
                elements.orgChart = chartElement;
            } else {
                console.log('Found existing org-chart element');
                elements.orgChart = chartElement;
            }
        } else {
            console.error('Org chart container element not found');
            return;
        }
    }

    console.log('Updating organization chart');
    console.log('gameState:', gameState);
    console.log('Current role:', gameState.currentRole);
    console.log('Completed roles:', gameState.completedRoles);
    console.log('Org chart HTML:', gameState.orgChartHtml ? 'Present' : 'Missing');
    if (gameState.orgChartHtml) {
        console.log('HTML content:', gameState.orgChartHtml.substring(0, 100) + '...');
    }

    // Always generate the org chart HTML manually to ensure it reflects the current role
    console.log('Generating org chart HTML manually to ensure current role is highlighted');
    console.log('CRITICAL DEBUG - Current role before generating chart:', gameState.currentRole);
    console.log('CRITICAL DEBUG - Completed roles before generating chart:', JSON.stringify(gameState.completedRoles || []));

    // Force refresh game state from server before generating chart
    fetch(API_ENDPOINTS.GET_GAME_STATE)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('CRITICAL DEBUG - Server current_role:', data.current_role);
                console.log('CRITICAL DEBUG - Server completed_roles:', JSON.stringify(data.completed_roles || []));

                // Update local game state with server data
                if (data.current_role) {
                    gameState.currentRole = data.current_role;
                }
                if (data.completed_roles) {
                    gameState.completedRoles = data.completed_roles;
                }

                // Now generate the chart with updated data
                const html = generateOrgChartHtml(gameState.currentRole, gameState.completedRoles || []);
                if (html) {
                    console.log('CRITICAL DEBUG - Generated org chart HTML with current role:', gameState.currentRole);

                    // Update main org chart
                    if (elements.orgChart) {
                        console.log('CRITICAL DEBUG - Updating main org chart element');
                        elements.orgChart.innerHTML = html;
                    }

                    // Also update mobile org chart if it exists
                    if (mobileOrgChart) {
                        console.log('CRITICAL DEBUG - Updating mobile org chart');
                        mobileOrgChart.innerHTML = html;
                    }

                    // Force a DOM refresh
                    setTimeout(() => {
                        console.log('CRITICAL DEBUG - Forcing DOM refresh');
                        if (elements.orgChart) {
                            const currentNodes = elements.orgChart.querySelectorAll('.org-node.current');
                            console.log('CRITICAL DEBUG - Current nodes found:', currentNodes.length);
                            currentNodes.forEach(node => {
                                console.log('CRITICAL DEBUG - Current node:', node.getAttribute('title'));
                            });
                        }
                    }, 100);
                }
            }
        })
        .catch(error => {
            console.error('Error fetching game state for org chart update:', error);

            // Fallback to using local state
            const html = generateOrgChartHtml(gameState.currentRole, gameState.completedRoles || []);
            if (html) {
                console.log('Generated org chart HTML with current role:', gameState.currentRole);

                // Remove any HTML comments that might contain "system undefined"
                html = html.replace(/<!--[\s\S]*?-->/g, '');

                // Update main org chart
                if (elements.orgChart) {
                    elements.orgChart.innerHTML = html;
                }

                // Also update mobile org chart if it exists
                if (mobileOrgChart) {
                    console.log('Updating mobile org chart');
                    mobileOrgChart.innerHTML = html;
                }
            }
        });

    // Check if we need to set a placeholder
    if (!gameState.orgChartHtml) {
        console.log('Setting placeholder for org chart');
        // Fallback to a placeholder
        const placeholder = '<div class="org-chart-placeholder">Organization chart loading...</div>';

        if (elements.orgChart) {
            elements.orgChart.innerHTML = placeholder;
        }

        if (mobileOrgChart) {
            mobileOrgChart.innerHTML = placeholder;
        }

        // Try to fetch the org chart HTML from the server as a fallback
        console.log('Attempting to fetch org chart HTML from server as fallback');
        fetch(API_ENDPOINTS.GET_GAME_STATE)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success' && data.org_chart_html) {
                    console.log('Received org chart HTML from server');
                    // Modify the HTML to highlight the current role
                    let modifiedHtml = data.org_chart_html;
                    // Add a class to highlight the current role
                    const roleClass = `data-position="${gameState.currentRole}"`;
                    const highlightClass = 'class="org-node current"';
                    modifiedHtml = modifiedHtml.replace(
                        new RegExp(`class="org-node[^"]*"[^>]*${roleClass}`, 'g'),
                        highlightClass + ' ' + roleClass
                    );

                    // Update both charts
                    if (elements.orgChart) {
                        elements.orgChart.innerHTML = modifiedHtml;
                    }

                    if (mobileOrgChart) {
                        mobileOrgChart.innerHTML = modifiedHtml;
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching org chart HTML:', error);
            });
    }
}

// Add a message to the UI
function addMessageToUI(message) {
    console.log('Adding message to UI:', message);

    // BUGFIX: Enhanced duplicate detection to prevent duplicate messages in the UI
    // This is especially important for task messages that might have different IDs but the same content
    const existingMessages = Array.from(elements.messagesContainer.querySelectorAll('.message'));

    // First, check for exact duplicates by ID
    const isDuplicateById = existingMessages.some(el => {
        return el.dataset.messageId === message.id;
    });

    if (isDuplicateById) {
        console.log('BUGFIX - Skipping duplicate message with ID:', message.id);
        return;
    }

    // For task messages, check for duplicates by task_id, sender, and content
    if (message.is_challenge && message.task_id) {
        const isDuplicateTask = existingMessages.some(el => {
            // Check if this is the same task from the same sender
            const sameTaskAndSender = el.dataset.taskId === message.task_id &&
                                      el.dataset.sender === message.sender;

            // If it's the same task and sender, also check if the content is similar
            // This helps catch duplicates even if they have different IDs
            if (sameTaskAndSender) {
                // Get the message text content from the element
                const messageTextEl = el.querySelector('.message-text');
                if (messageTextEl) {
                    // Compare the first 100 characters of the message text
                    // This should be enough to identify duplicates without being too strict
                    const existingText = messageTextEl.textContent.substring(0, 100);
                    const newText = message.text.substring(0, 100);

                    // If the first 100 characters are the same, consider it a duplicate
                    if (existingText === newText) {
                        console.log('BUGFIX - Skipping duplicate task message with same content for task_id:', message.task_id);
                        return true;
                    }
                }
            }
            return false;
        });

        if (isDuplicateTask) {
            console.log('BUGFIX - Task message already exists in UI, not adding duplicate');
            return;
        }
    }

    // Handle the case where is_promotion might be undefined
    // Default to false if not present
    if (message.is_promotion === undefined) {
        message.is_promotion = false;
        console.log('BUGFIX - is_promotion was undefined, setting to false');
    }

    // We're already filtering out system messages in updateUI, but add a safety check here too
    // Always show task messages (is_challenge), promotion messages (is_promotion), and resent messages even if they're from system
    if (message.sender === 'system' && !message.is_challenge && !message.is_resent && !message.is_promotion) {
        console.log('Skipping system message that is not a challenge, resent, or promotion');
        return;
    }

    const messageElement = document.createElement('div');
    messageElement.className = `message ${message.sender === 'user' ? 'from-you' : 'from-them'}`;

    // Store message ID and task_id as data attributes for duplicate detection
    messageElement.dataset.messageId = message.id;
    if (message.task_id) {
        messageElement.dataset.taskId = message.task_id;
    }
    messageElement.dataset.sender = message.sender;

    // Add is-resent class if this is a resent message
    if (message.is_resent) {
        messageElement.classList.add('is-resent');
    }

    // Initialize messageText variable first to avoid "Cannot access before initialization" error
    // Check if this message has HTML content (for markdown)
    let messageText = message.text;
    if (message.is_markdown && message.html) {
        messageText = message.html;
    } else if (message.content) {
        // Handle case where content is provided instead of text (for promotion messages)
        messageText = message.content;
    }

    // Add is-promotion class if this is a promotion message
    if (message.is_promotion) {
        messageElement.classList.add('is-promotion');
        // We now want to show promotion messages in the message container
        // They will be shown before the next task message
        console.log('Adding promotion message to message container');

        // Format the promotion message to make it more prominent
        // This matches the Flask app's style
        if (message.html && message.html.includes('Congratulations')) {
            // Clean up duplicate congratulations headers if they exist
            const congratsPattern = /Congratulations/g;
            const matches = message.html.match(congratsPattern);

            if (matches && matches.length > 1) {
                // Keep only one instance of congratulations
                let firstIndex = message.html.indexOf('Congratulations');
                let secondIndex = message.html.indexOf('Congratulations', firstIndex + 1);

                if (firstIndex !== -1 && secondIndex !== -1) {
                    // Remove the second congratulations and any duplicate emojis
                    message.html = message.html.substring(0, secondIndex) +
                                  message.html.substring(secondIndex)
                                      .replace('Congratulations', '')
                                      .replace('! 🎉', '');
                }
            }

            // Add some styling to make it more prominent
            messageText = message.html.replace(
                /Congratulations/g,
                '<h2>🎉 Congratulations! 🎉</h2>'
            );

            // Add CEO title to Joy Nantongo if it's not already there
            if (messageText.includes('Joy Nantongo') && !messageText.includes('Chief Executive Officer')) {
                // Replace with properly styled CEO name
                messageText = messageText.replace(
                    /<div class="message-sender">Joy Nantongo<\/div>/,
                    '<div class="message-sender message-sender-ceo">Joy Nantongo, Chief Executive Officer (CEO)</div>'
                );

                // Also handle plain text version
                messageText = messageText.replace(
                    /Joy Nantongo(?!,)/g,
                    'Joy Nantongo, Chief Executive Officer (CEO)'
                );

                console.log('Added CEO title to Joy Nantongo in promotion message display');
            }

            // IMPORTANT: We don't add manager information here anymore
            // Manager information is now only added in the handlePromotion function
            // to the most recent promotion message

            // The manager information should already be in the message HTML if it's needed
            // We just need to preserve it when displaying the message

            // Add a note about the next task
            if (!messageText.includes('Your next task will appear shortly')) {
                messageText += '<div class="promotion-note">Your next task will appear shortly...</div>';
            }

            // Add the "How to Improve Your Request" and "Try these tips" sections
            // This matches the Flask app's style but with improved styling
            if (!messageText.includes('How to Improve Your Request')) {
                messageText += `
                    <div class="improvement-section">
                        <h3>How to Improve Your Request:</h3>
                        <div class="tips-section">
                            <h4>Try these tips:</h4>
                            <ul>
                                <li>Be specific about what you want in your next task</li>
                                <li>Provide context about your situation</li>
                                <li>Specify the format you prefer for the response</li>
                            </ul>
                        </div>
                    </div>
                `;
            }
        }

        // Also check for text content if html is not available
        if (!message.html && message.text && message.text.includes('Congratulations')) {
            // Add some styling to make it more prominent
            messageText = message.text.replace(
                /Congratulations/g,
                '🎉 Congratulations! 🎉'
            );

            // Add CEO title to Joy Nantongo if it's not already there
            if (messageText.includes('Joy Nantongo') && !messageText.includes('Chief Executive Officer')) {
                messageText = messageText.replace(/Joy Nantongo(?!,)/g, 'Joy Nantongo, Chief Executive Officer (CEO)');
                console.log('Added CEO title to Joy Nantongo in text-only promotion message');
            }

            // IMPORTANT: We don't add manager information here anymore
            // Manager information is now only added in the handlePromotion function
            // to the most recent promotion message

            // The manager information should already be in the message text if it's needed
            // We just need to preserve it when displaying the message

            // Add a note about the next task if it's not already there
            if (!messageText.includes('Your next task will appear shortly')) {
                messageText += '\n\nYour next task will appear shortly...';
            }
        }
    }

    // Get character info - FIXED to ensure user messages show as "You"
    let characterName = message.sender === 'user' ? 'You' : (message.sender === 'ai' ? 'AI Assistant' : (gameState.characters[message.sender]?.name || message.sender));
    if (message.sender === 'ceo') {
        characterName = `${gameState.characters.ceo.name}, ${gameState.characters.ceo.title}`;
    }
    const character = {
        name: characterName,
        color: characterColors[message.sender] || (message.sender === 'user' ? characterColors.user : (message.sender === 'ai' ? characterColors.ai : '#888888'))
    };

    // Update character info in sidebar if not user or AI
    if (message.sender !== 'user' && message.sender !== 'ai' && message.is_challenge) {
        updateCharacterInfo(message.sender);

        // Update current task and manager if this is a challenge message
        if (message.task_id) {
            gameState.currentTask = message.task_id;
            gameState.currentManager = message.sender;
            console.log(`Updated to task: ${gameState.currentTask}, manager: ${gameState.currentManager}`);
        }
    }

    // We've already initialized messageText above, so this section is no longer needed

    // Create message HTML
    // Add ai-response class if the message is from the AI assistant
    const messageContentClass = message.sender === 'ai' ? 'message-content ai-response' : 'message-content';

    // Add special class for CEO messages
    let senderClass = 'message-sender';
    if (character.name.includes('Chief Executive Officer') || character.name.includes('CEO')) {
        senderClass += ' message-sender-ceo';
    }

    messageElement.innerHTML = `
        <div class="avatar" style="background-color: ${character.color}">
            ${getAvatarEmoji(message.sender)}
        </div>
        <div class="${messageContentClass}">
            <div class="${senderClass}">${character.name}</div>
            <div class="message-text">${messageText}</div>
            <div class="message-time">${formatTime(message.timestamp)}</div>
        </div>
    `;

    elements.messagesContainer.appendChild(messageElement);
}

// Update character info in the sidebar
function updateCharacterInfo(characterId) {
    const character = gameState.characters[characterId];
    if (!character) return;

    elements.characterAvatar.textContent = getAvatarEmoji(characterId);
    elements.characterAvatar.style.backgroundColor = characterColors[characterId] || '#888888';
    elements.characterName.textContent = character.name;
    elements.characterTitle.textContent = character.title;
}

// Get avatar emoji based on character ID
function getAvatarEmoji(characterId) {
    const avatars = {
        user: '👤',
        ai: '🤖',
        hr: '👩‍💼',
        manager: '👨‍💼',
        vp: '👩‍💼',
        coo: '👨‍💼',
        ceo: '👩‍💼',
        board: '👥',
        system: '🖥️'
    };

    return avatars[characterId] || '👤';
}

// Set the current step (prompt, preview, edit)
function setStep(step) {
    gameState.currentStep = step;

    // Hide all containers
    elements.promptContainer.classList.add('hidden');
    elements.previewContainer.classList.add('hidden');
    elements.editResponseContainer.classList.add('hidden');

    // Show the appropriate container
    switch (step) {
        case 'prompt':
            elements.promptContainer.classList.remove('hidden');
            elements.promptInput.focus();
            break;
        case 'preview':
            elements.previewContainer.classList.remove('hidden');
            break;
        case 'edit':
            elements.editResponseContainer.classList.remove('hidden');
            elements.responseEditor.focus();
            break;
    }
}

// Handle preview prompt button click
async function handlePreviewPrompt() {
    const prompt = elements.promptInput.value.trim();

    if (!prompt) {
        alert('Please enter a prompt first');
        return;
    }

    showLoading();

    try {
        // Add user message to UI
        const userMessage = {
            id: generateId(),
            sender: 'user',
            text: prompt,
            timestamp: new Date().toISOString()
        };

        gameState.messages.push(userMessage);
        addMessageToUI(userMessage);

        // Send prompt to server for preview using the previewResponse function
        const data = await previewResponse(prompt);

        if (data.status === 'success') {
            // Store preview data
            gameState.previewData = {
                prompt: prompt,
                aiResponse: data.ai_response,
                aiResponseHtml: data.ai_response_html,
                grade: data.prompt_evaluation_grade,
                similarityScore: data.similarity_score,
                feedbackDetails: data.feedback_details,
                sectionScores: data.section_scores || [],
                meetsRequirements: data.meets_requirements || false,
                overallScore: data.overall_score || 0,
                improvementFeedback: data.improvement_feedback || '',
                // New prompt evaluation data
                promptDimensions: data.prompt_dimensions || {},
                promptImprovementSuggestions: data.prompt_improvement_suggestions || [],
                promptEvaluationSummary: data.prompt_evaluation_summary || '',
                // Manager feedback
                previewFeedback: data.preview_feedback || '',
                // Offline mode indicator
                isOffline: data.is_offline || false
            };

            // Update preview content - use HTML if available
            if (data.ai_response_html) {
                elements.previewContent.innerHTML = data.ai_response_html;
            } else if (data.html_response) {
                elements.previewContent.innerHTML = data.html_response;
            } else {
                elements.previewContent.textContent = data.ai_response;
            }

            // Update manager feedback preview if available
            const managerFeedbackContent = document.getElementById('manager-feedback-content');
            if (managerFeedbackContent && data.preview_feedback) {
                managerFeedbackContent.innerHTML = data.preview_feedback;
                document.getElementById('manager-feedback-container').classList.remove('hidden');
            } else if (managerFeedbackContent) {
                document.getElementById('manager-feedback-container').classList.add('hidden');
            }

            // Update quality indicator to match Flask app
            const grade = data.prompt_evaluation_grade || 'okay';
            if (data.is_offline) {
                elements.qualityIndicator.textContent = `QUALITY: ${grade.toUpperCase()} (OFFLINE)`;
                elements.qualityIndicator.className = `quality-indicator ${grade} offline`;
            } else {
                elements.qualityIndicator.textContent = `QUALITY: ${grade.toUpperCase()}`;
                elements.qualityIndicator.className = `quality-indicator ${grade}`;
            }

            // Update similarity score and feedback details if available
            if (data.similarity_score !== undefined && elements.similarityContainer) {
                // Show similarity container
                elements.similarityContainer.classList.remove('hidden');

                // Update similarity score
                if (elements.similarityScore) {
                    elements.similarityScore.textContent = `${data.similarity_score}`;
                }

                // Update feedback details
                if (elements.feedbackDetailsList && data.feedback_details && Array.isArray(data.feedback_details)) {
                    // Clear existing feedback details
                    elements.feedbackDetailsList.innerHTML = '';

                    // Add each feedback detail as a list item
                    data.feedback_details.forEach(detail => {
                        const li = document.createElement('li');
                        li.textContent = detail;

                        // Check if this is an offline notice
                        if (detail.includes('OFFLINE MODE')) {
                            li.className = 'offline-notice';
                        }

                        elements.feedbackDetailsList.appendChild(li);
                    });
                }

                // Always show the prompt evaluation container to match Flask app
                if (elements.promptEvaluationContainer && elements.dimensionsGrid) {
                    // Show the container
                    elements.promptEvaluationContainer.classList.remove('hidden');

                    // Update summary if available
                    if (elements.promptEvaluationSummary && data.prompt_evaluation_summary) {
                        elements.promptEvaluationSummary.textContent = data.prompt_evaluation_summary;
                    } else if (elements.promptEvaluationSummary) {
                        // Default summary if not provided
                        elements.promptEvaluationSummary.textContent = "This is a very strong prompt overall. It provides clear instructions, sufficient context, and is well-aligned with the capabilities of an AI. The main area for improvement is adding more specific output constraints regarding the desired length, tone, and style of the cover letter.";
                    }

                    // Clear existing dimensions
                    elements.dimensionsGrid.innerHTML = '';

                    // Add each dimension as a card if available
                    if (data.prompt_dimensions && Object.keys(data.prompt_dimensions).length > 0) {
                        Object.entries(data.prompt_dimensions).forEach(([name, info]) => {
                            const score = info.score || 0;
                            const feedback = info.feedback || '';
                            const suggestions = info.suggestions || [];

                            // Create dimension card
                            const card = document.createElement('div');
                            card.className = 'dimension-card';

                            // Determine score class
                            let scoreClass = 'medium';
                            if (score >= 75) scoreClass = 'high';
                            else if (score < 50) scoreClass = 'low';

                            // Create more user-friendly dimension names
                            const dimensionNames = {
                                'clarity': 'Clear Instructions',
                                'context': 'Background Info',
                                'completeness': 'Complete Details',
                                'task_alignment': 'Purpose',
                                'output_constraints': 'Format Preferences',
                                'model_awareness': 'Reasonable Request'
                            };

                            const friendlyName = dimensionNames[name] || name.charAt(0).toUpperCase() + name.slice(1);

                            // Create card content with more user-friendly display
                            card.innerHTML = `
                                <div class="dimension-header">
                                    <div class="dimension-name">${friendlyName}</div>
                                    <div class="dimension-score ${scoreClass}">${score}/100</div>
                                </div>
                                <div class="dimension-feedback">${feedback}</div>
                                ${suggestions.length > 0 ? `<div class="dimension-suggestions">Tip: ${suggestions[0]}</div>` : ''}
                            `;

                            elements.dimensionsGrid.appendChild(card);
                        });
                    } else {
                        // Add default dimension cards if no data is provided
                        const defaultDimensions = [
                            { name: 'Clear Instructions', score: 70, feedback: 'Your instructions are generally clear.', suggestion: 'Be more specific about what you want.' },
                            { name: 'Background Info', score: 65, feedback: 'You provided some context.', suggestion: 'Add more details about the situation.' },
                            { name: 'Complete Details', score: 60, feedback: 'Your prompt includes basic details.', suggestion: 'Include more specific requirements.' }
                        ];

                        defaultDimensions.forEach(dim => {
                            const card = document.createElement('div');
                            card.className = 'dimension-card';

                            // Determine score class
                            let scoreClass = 'medium';
                            if (dim.score >= 75) scoreClass = 'high';
                            else if (dim.score < 50) scoreClass = 'low';

                            // Create card content
                            card.innerHTML = `
                                <div class="dimension-header">
                                    <div class="dimension-name">${dim.name}</div>
                                    <div class="dimension-score ${scoreClass}">${dim.score}/100</div>
                                </div>
                                <div class="dimension-feedback">${dim.feedback}</div>
                                <div class="dimension-suggestions">Tip: ${dim.suggestion}</div>
                            `;

                            elements.dimensionsGrid.appendChild(card);
                        });
                    }

                    // Update improvement suggestions
                    if (elements.promptImprovementList) {
                        // Clear existing suggestions
                        elements.promptImprovementList.innerHTML = '';

                        // Add suggestions if available
                        if (data.prompt_improvement_suggestions && data.prompt_improvement_suggestions.length > 0) {
                            data.prompt_improvement_suggestions.forEach(suggestion => {
                                const li = document.createElement('li');
                                li.textContent = suggestion;
                                elements.promptImprovementList.appendChild(li);
                            });
                        } else {
                            // Add default suggestions if none provided
                            const defaultSuggestions = [
                                'Specify the desired length of the cover letter (e.g., one page under 500 words).',
                                'Indicate the desired tone or style (e.g., formal, enthusiastic, confident).',
                                'Briefly mention any specific skills or experiences you want to emphasize in the cover letter.'
                            ];

                            defaultSuggestions.forEach(suggestion => {
                                const li = document.createElement('li');
                                li.textContent = suggestion;
                                elements.promptImprovementList.appendChild(li);
                            });
                        }
                    }
                }

                // These sections are intentionally removed to match the Flask app
                if (elements.enhancedEvaluationContainer) {
                    // Hide enhanced evaluation container to match Flask app
                    elements.enhancedEvaluationContainer.classList.add('hidden');
                }

                if (elements.sectionScoresContainer) {
                    // Hide section scores container to match Flask app
                    elements.sectionScoresContainer.classList.add('hidden');
                }
            } else if (elements.similarityContainer) {
                // Hide similarity container if no similarity score is available
                elements.similarityContainer.classList.add('hidden');

                // Also hide section scores container
                if (elements.sectionScoresContainer) {
                    elements.sectionScoresContainer.classList.add('hidden');
                }
            }

            // Update org chart HTML if provided
            if (data.org_chart_html) {
                gameState.orgChartHtml = data.org_chart_html;
                updateOrgChart();
            }

            // Update role progression HTML if provided
            if (data.role_progression_html) {
                gameState.roleProgressionHtml = data.role_progression_html;
                updateRoleProgression();
            }

            // Set response editor content
            elements.responseEditor.value = data.ai_response;

            // Move to preview step
            setStep('preview');
        } else {
            console.error('Preview failed:', data.message);

            // Check if this is an offline error
            if (data.error_type === 'offline' || data.is_offline) {
                // Show the offline mode alert modal instead of a basic alert
                const offlineAlert = document.getElementById('offline-mode-alert');
                offlineAlert.classList.remove('hidden');

                // Add event listener to close button
                const closeOfflineAlertButton = document.getElementById('close-offline-alert-button');
                closeOfflineAlertButton.onclick = function() {
                    offlineAlert.classList.add('hidden');
                };
            } else {
                // Generic error message for other errors
                alert('Failed to generate preview. Please try again.');
            }
        }
    } catch (error) {
        console.error('Error previewing prompt:', error);
        alert('Error connecting to the server. Please check your connection and try again.');
    } finally {
        hideLoading();
    }
}

// Handle submit response button click
async function handleSubmitResponse() {
    if (!gameState.previewData) {
        alert('No preview data available. Please try again.');
        return;
    }

    // Submit the unedited response
    await submitResponse(gameState.previewData.aiResponse);
}

// Handle submit edited response button click
async function handleSubmitEditedResponse() {
    const editedResponse = elements.responseEditor.value.trim();

    if (!editedResponse) {
        alert('Please enter a response');
        return;
    }

    await submitResponse(editedResponse);
}

// Submit the response to the server
async function submitResponse(responseText) {
    showLoading();

    try {
        console.log('Submitting response with:', {
            prompt: gameState.previewData.prompt,
            edited_response: responseText,
            current_manager: gameState.currentManager,
            current_task: gameState.currentTask
        });

        const response = await fetch(API_ENDPOINTS.SUBMIT_PROMPT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                prompt: gameState.previewData.prompt,
                edited_response: responseText,
                current_manager: gameState.currentManager,
                current_task: gameState.currentTask
            })
        });

        console.log('Response received from server');

        const data = await response.json();
        console.log('Response data:', data);

        // Log the similarity score from the server
        console.log('DEBUG - Server similarity_score:', data.similarity_score);
        console.log('DEBUG - Server overall_score:', data.overall_score);

        // Store the original response data for debugging
        window.originalResponseData = data;

        // Store the response data for debugging
        window.lastResponseData = data;

        if (data.status === 'error' && (data.error_type === 'offline' || data.is_offline)) {
            // Handle offline error with modal
            const offlineAlert = document.getElementById('offline-mode-alert');
            offlineAlert.classList.remove('hidden');

            // Add event listener to close button
            const closeOfflineAlertButton = document.getElementById('close-offline-alert-button');
            closeOfflineAlertButton.onclick = function() {
                offlineAlert.classList.add('hidden');
            };

            hideLoading();
            return;
        } else if (data.status === 'success') {
            // Get the similarity score (which is on a 0-100 scale)
            // Use overall_score if available, otherwise fall back to similarity_score
            let similarityScore = data.overall_score || data.similarity_score || 0;

            // Make sure the similarity score is on a 0-100 scale
            if (similarityScore <= 10) {
                similarityScore = similarityScore * 10;
                console.log('DEBUG - Converting score from 0-10 to 0-100 scale:', similarityScore);
            }

            console.log('DEBUG - Using score for calculations:', similarityScore);

            // Calculate the performance score (which is on a 0-10 scale)
            // This is simply the similarity score divided by 10
            const performanceScore = similarityScore / 10;

            // Determine if the task meets requirements - USE THE SERVER'S VALUE
            const meetsRequirements = data.meets_requirements === true;

            // Get the grade
            const grade = data.grade || (meetsRequirements ? 'good' : 'okay');

            // Get the manager name
            let managerName = data.manager_name;
            if (!managerName && data.current_manager && gameState.characters && gameState.characters[data.current_manager]) {
                managerName = gameState.characters[data.current_manager].name;
            }

            console.log('DEBUG - SCORE CALCULATION:', {
                similarityScore: similarityScore,
                performanceScore: performanceScore,
                meetsRequirements: meetsRequirements,
                manager: data.current_manager,
                managerName: managerName,
                task: data.current_task,
                grade: grade
            });

            // Store the processed data for debugging and for the task failure modal
            const processedData = {
                similarity_score: similarityScore,
                overall_score: similarityScore, // Store as both similarity_score and overall_score
                performance_score: performanceScore,
                manager: data.current_manager,
                manager_name: managerName,
                current_task: data.current_task,
                grade: grade,
                meets_requirements: meetsRequirements
            };

            // Store this data globally for access in the task failure modal
            window.lastResponseData = processedData;

            console.log('DEBUG - PROCESSED SCORES:', processedData);

            // Update messages regardless of whether the task passed or failed
            gameState.messages = data.messages || [];

            // Determine if the task should pass based on the meets_requirements flag
            const shouldPass = meetsRequirements === true;

            console.log('Task evaluation:', {
                similarityScore,
                performanceScore,
                meetsRequirements,
                shouldPass,
                grade,
                manager: data.current_manager,
                managerName: managerName
            });

            if (shouldPass) {
                // Task passed - update game state for progression
                gameState.currentRole = data.current_role || gameState.currentRole;
                gameState.performanceScore = data.performance_score || 0;
                gameState.challengesCompleted = data.total_challenges_completed || gameState.challengesCompleted + 1;

                // BUGFIX: Properly handle role challenges completed count
                // CRITICAL FIX: Always use the server-provided value to avoid double-counting
                if (data.role_challenges_completed !== undefined) {
                    // Always trust the server's value
                    gameState.roleChallengesCompleted = data.role_challenges_completed;
                    console.log('BUGFIX - Using server-provided roleChallengesCompleted:', gameState.roleChallengesCompleted);

                    // CRITICAL FIX: Ensure roleChallengesCompleted never exceeds challenges required
                    // This prevents skipping the last task due to double-counting
                    const challengesRequired = data.challenges_required || gameState.challengesRequired || 3;
                    if (gameState.roleChallengesCompleted > challengesRequired) {
                        console.log('CRITICAL FIX - roleChallengesCompleted exceeds challengesRequired, capping at:', challengesRequired);
                        gameState.roleChallengesCompleted = challengesRequired;
                    }
                } else {
                    // If server doesn't provide a value (which shouldn't happen), don't increment
                    // This prevents double-counting of tasks
                    console.log('BUGFIX - Server did not provide roleChallengesCompleted, keeping current value:', gameState.roleChallengesCompleted);
                }

                gameState.gameCompleted = data.game_completed || false;
                gameState.completedRoles = data.completed_roles || gameState.completedRoles || [];
                gameState.challengesRequired = data.challenges_required || 3;

                // Update current manager and task if provided
                if (data.current_manager) {
                    gameState.currentManager = data.current_manager;
                    console.log('Updated current manager to:', gameState.currentManager);

                    // BUGFIX: Update character info immediately when manager changes
                    if (gameState.characters && gameState.characters[gameState.currentManager]) {
                        updateCharacterInfo(gameState.currentManager);
                    }
                }

                if (data.current_task) {
                    gameState.currentTask = data.current_task;
                    console.log('Updated current task to:', gameState.currentTask);
                }

                // Update org chart HTML if provided
                if (data.org_chart_html) {
                    gameState.orgChartHtml = data.org_chart_html;
                }

                // Update role progression HTML if provided
                if (data.role_progression_html) {
                    gameState.roleProgressionHtml = data.role_progression_html;
                }

                console.log(`Updated to manager: ${gameState.currentManager}, task: ${gameState.currentTask}`);
                console.log(`Role: ${gameState.currentRole}, Role challenges: ${gameState.roleChallengesCompleted}`);

                // Reinitialize chart zoom functionality after game state update
                if (window.reinitializeChartZoom) {
                    console.log('Reinitializing chart zoom functionality after game state update');
                    window.reinitializeChartZoom();
                }

                // Update UI
                updateUI();

                // Log debug information about promotion status
                console.log('DEBUG - Promotion status:', {
                    promoted: data.promoted,
                    is_promotion: data.is_promotion,
                    just_completed_task: data.just_completed_task,
                    current_role: gameState.currentRole,
                    role_challenges_completed: data.role_challenges_completed,
                    challenges_required: data.challenges_required,
                    debug_info: data.debug_info
                });

                // Check for promotion - this should only be true when the player has just been promoted to a new role
                // Handle the case where promoted might be undefined
                const isPromoted = data.promoted === true ||
                                  (data.debug_info && data.debug_info.promoted === true);

                if (isPromoted) {
                    // This is a true promotion to a new role
                    console.log(`PROMOTION EVENT: Player promoted to ${gameState.currentRole}!`);
                    console.log(`DEBUG - next_task_pending from server: ${data.next_task_pending}`);

                    // Log debug info if available
                    if (data.debug_info) {
                        console.log('DEBUG - Server debug info:', data.debug_info);
                    }

                    // BUGFIX: Always set nextTaskPending to true after promotion to ensure next task is fetched
                    gameState.nextTaskPending = true;
                    console.log(`DEBUG - nextTaskPending in gameState: ${gameState.nextTaskPending}`);

                    // Call our handlePromotion function to update the org chart and add promotion message
                    handlePromotion(gameState.currentRole, data.completed_roles || []);

                    // BUGFIX: Reset role_challenges_completed to 0 after promotion
                    // This is critical for proper role progress tracking
                    gameState.roleChallengesCompleted = 0;
                    console.log('BUGFIX - Reset roleChallengesCompleted to 0 after promotion');

                    // Force update the role progress display immediately
                    if (elements.roleProgress) {
                        let requiredChallenges = gameState.challengesRequired || 3;
                        elements.roleProgress.textContent = `${gameState.roleChallengesCompleted}/${requiredChallenges}`;
                        console.log('BUGFIX - Immediately updated role progress display to:', elements.roleProgress.textContent);
                    }

                    // BUGFIX: Force update the UI to show the reset counter
                    updateUI();

                    // BUGFIX: Fetch the next task after promotion, but only once
                    console.log('DEBUG - Setting up a single call to fetchNextTask after promotion');

                    // Clear any existing timeouts to prevent multiple calls
                    if (window.nextTaskTimeout) {
                        console.log('BUGFIX - Clearing existing nextTaskTimeout');
                        clearTimeout(window.nextTaskTimeout);
                    }

                    // Clear any second attempt timeout
                    if (window.secondAttemptTimeout) {
                        console.log('BUGFIX - Clearing existing secondAttemptTimeout');
                        clearTimeout(window.secondAttemptTimeout);
                    }

                    // Clear any third attempt timeout
                    if (window.thirdAttemptTimeout) {
                        console.log('BUGFIX - Clearing existing thirdAttemptTimeout');
                        clearTimeout(window.thirdAttemptTimeout);
                    }

                    // First attempt - only if not already fetching
                    if (!fetchingNextTask) {
                        console.log('DEBUG - Making first attempt to fetch next task');
                        fetchNextTask();
                    } else {
                        console.log('BUGFIX - Already fetching next task, skipping first attempt');
                    }

                    // Second attempt - after 3 seconds, only if needed
                    window.secondAttemptTimeout = setTimeout(() => {
                        console.log('DEBUG - Second attempt timeout triggered');
                        if (gameState.nextTaskPending && !fetchingNextTask) {
                            console.log('DEBUG - Still need to fetch next task, making second attempt');
                            fetchNextTask();
                        } else {
                            console.log('BUGFIX - No need for second attempt, task already fetched or in progress');
                        }
                        // Force update UI again
                        updateUI();
                    }, 3000);

                    // Third attempt - after 6 seconds, only if needed
                    window.thirdAttemptTimeout = setTimeout(() => {
                        console.log('DEBUG - Third attempt timeout triggered');
                        if (gameState.nextTaskPending && !fetchingNextTask) {
                            console.log('DEBUG - Still need to fetch next task, making third attempt');
                            fetchNextTask();
                        } else {
                            console.log('BUGFIX - No need for third attempt, task already fetched or in progress');
                        }
                        // Force update UI again
                        updateUI();
                    }, 6000);

                    // Fourth attempt - after 10 seconds with manual button
                    setTimeout(() => {
                        console.log('DEBUG - Fourth attempt timeout triggered');
                        // Force update UI again
                        updateUI();

                        // Manual next task button removed - tasks now progress automatically
                        if (gameState.nextTaskPending) {
                            console.log('DEBUG - Attempting one more automatic fetch of next task');
                            try {
                                fetchNextTask();
                            } catch (error) {
                                console.error('Error in final automatic fetch attempt:', error);
                            }
                        }
                    }, 10000);

                    // Show promotion celebration with confetti
                    if (typeof showPromotionCelebration === 'function') {
                        console.log('Showing promotion celebration modal');
                        showPromotionCelebration(gameState.currentRole);
                    } else {
                        // Fallback if celebration.js is not loaded
                        console.log('Celebration.js not loaded, using fallback promotion sound');
                        const promotionSound = new Audio('https://freesound.org/data/previews/270/270402_5123827-lq.mp3');
                        try {
                            promotionSound.volume = 0.5;
                            promotionSound.play().catch(error => console.log('Could not play promotion sound:', error));
                        } catch (error) {
                            console.log('Could not play promotion sound:', error);
                        }
                    }
                } else if (data.just_completed_task === true ||
                          (data.debug_info && data.debug_info.just_completed_task === true)) {
                    // Task completed but no promotion yet
                    console.log(`TASK COMPLETION EVENT: Task completed: ${gameState.currentTask}`);

                    // Log debug info if available
                    if (data.debug_info) {
                        console.log('DEBUG - Server debug info for task completion:', data.debug_info);
                    }

                    // Set the taskCompleted flag to true
                    gameState.taskCompleted = true;
                    console.log('DEBUG - Setting taskCompleted flag to true');

                    // Also set nextTaskPending to true to ensure we can fetch the next task
                    gameState.nextTaskPending = true;
                    console.log('DEBUG - Setting nextTaskPending flag to true for task completion');

                    // BUGFIX: Never increment roleChallengesCompleted here - rely only on server value
                    // This prevents double-counting of tasks
                    console.log('BUGFIX - Using server-provided roleChallengesCompleted:', data.role_challenges_completed);

                    // Force update the role progress display immediately with server value
                    if (elements.roleProgress) {
                        let requiredChallenges = gameState.challengesRequired || 3;
                        elements.roleProgress.textContent = `${gameState.roleChallengesCompleted}/${requiredChallenges}`;
                        console.log('BUGFIX - Immediately updated role progress display for task completion to:', elements.roleProgress.textContent);
                    }

                    // BUGFIX: Only fetch the next task if we're not on the last task
                    // This prevents the system from trying to fetch a non-existent next task
                    const isLastTask = gameState.roleChallengesCompleted >= (gameState.challengesRequired - 1);

                    if (!isLastTask) {
                        // Automatically fetch the next task after a short delay
                        console.log('BUGFIX - Setting up automatic fetch of next task after task completion');
                        setTimeout(() => {
                            console.log('BUGFIX - Auto-fetching next task after task completion');
                            if (!fetchingNextTask) {
                                fetchNextTask();
                            }
                        }, 3000); // 3 second delay
                    } else {
                        console.log('BUGFIX - On the last task of the role, not fetching next task');
                        // Reset the flags to prevent any automatic fetching
                        gameState.nextTaskPending = false;
                        gameState.taskCompleted = false;
                    }

                    // Show task completion celebration
                    if (typeof showTaskCompletionCelebration === 'function') {
                        console.log('Showing task completion celebration modal');
                        showTaskCompletionCelebration(gameState.currentTask);
                    } else {
                        console.log('Celebration.js not loaded, no task completion celebration shown');
                    }

                    // BUGFIX: Removed the manual next task button and all its logic
                    // Instead, we'll rely on automatic task progression

                    // Remove any existing manual next task button
                    const manualNextTaskButton = document.getElementById('manual-next-task-button');
                    if (manualNextTaskButton) {
                        console.log('BUGFIX - Removing manual next task button');
                        manualNextTaskButton.remove();
                    }

                    // Set a timeout to fetch the next task automatically after a delay
                    console.log('DEBUG - Setting timeout to fetch next task after task completion');
                    setTimeout(() => {
                        console.log('DEBUG - Timeout triggered, attempting to fetch next task');
                        // Only call fetchNextTask if we're not already fetching
                        if (!fetchingNextTask) {
                            fetchNextTask();
                        } else {
                            console.log('BUGFIX - Already fetching next task, skipping automatic timeout call');
                        }

                        // Remove the manual button if it still exists
                        const button = document.getElementById('manual-next-task-button');
                        if (button) {
                            button.remove();
                        }
                    }, 5000); // 5 second delay to allow user to read the celebration
                } else {
                    console.log('No celebration shown - neither promotion nor task completion detected');
                    // Reset the taskCompleted flag
                    gameState.taskCompleted = false;
                }

                // Check if game is completed
                if (data.game_completed) {
                    showGameCompleteModal();
                }
            } else {
                // Task failed - show failure modal and don't progress
                console.log('DEBUG - TASK FAILED CASE');
                console.log('DEBUG - Task failed. Grade:', grade, 'Meets requirements:', meetsRequirements);
                console.log('DEBUG - Scores at failure point:', {
                    performanceScore: performanceScore,
                    similarityScore: similarityScore,
                    manager: data.current_manager,
                    manager_name: data.manager_name,
                    current_task: data.current_task
                });

                // BUGFIX: Ensure we stay on the same task when a task fails
                // This is critical for game progression - users must complete the current task before moving on
                // This matches Sarah Chen's behavior where users must redo failed tasks

                // Log the current task that failed
                console.log(`DEBUG - Task failed: ${gameState.currentTask} with manager ${gameState.currentManager}`);
                console.log(`DEBUG - User will need to retry this task with a better response`);

                // Get the last message which should be the feedback
                let feedbackText = 'Your submission did not meet the requirements. Please try again.';
                if (gameState.messages.length > 0) {
                    const lastMessage = gameState.messages[gameState.messages.length - 1];
                    console.log('DEBUG - Last message for feedback:', lastMessage);
                    if (lastMessage.html) {
                        feedbackText = lastMessage.html;
                        console.log('DEBUG - Using HTML feedback from last message');
                    } else if (lastMessage.text) {
                        feedbackText = lastMessage.text;
                        console.log('DEBUG - Using text feedback from last message');
                    }
                }

                // Update UI to show the feedback message
                updateUI();

                // Show the task failure modal
                showTaskFailureModal(feedbackText);

                // Make sure the retry button is visible and properly labeled
                if (elements.retryTaskButton) {
                    elements.retryTaskButton.textContent = 'Retry This Task';
                    elements.retryTaskButton.style.display = 'block';
                }
            }
        } else {
            console.error('Submission failed:', data.message);
            alert('Failed to submit response. Please try again.');
        }
    } catch (error) {
        console.error('Error submitting response:', error);

        // BUGFIX: Add more detailed error handling and recovery
        // Store the current state so we can retry
        window.lastSubmissionAttempt = {
            prompt: gameState.previewData.prompt,
            edited_response: responseText,
            current_manager: gameState.currentManager,
            current_task: gameState.currentTask
        };

        // Show a helpful error message
        const errorMessage = 'Error connecting to the server. Please check your connection and try again.';

        // Just use alert for error messages since the failure modal has been removed
        alert(errorMessage + ' Your response has been saved and you can try submitting again.');
    } finally {
        hideLoading();
    }
}

// Show game complete modal
function showGameCompleteModal() {
    elements.finalScore.textContent = gameState.performanceScore;
    elements.gameCompleteModal.classList.remove('hidden');
}

// Show task failure modal
function showTaskFailureModal(feedbackText) {
    console.log('DEBUG - Showing task failure modal');

    // Get the response data from the global variable
    const lastResponseData = window.lastResponseData || {};

    // Log the scores for debugging purposes
    let similarityScore = lastResponseData.overall_score || lastResponseData.similarity_score || 0;
    if (similarityScore <= 10) {
        similarityScore = similarityScore * 10;
    }
    const performanceScore = (similarityScore / 10).toFixed(1);

    console.log('DEBUG - Task failure information:', {
        similarityScore: similarityScore,
        performanceScore: performanceScore,
        manager: lastResponseData.current_manager || gameState.currentManager,
        task: lastResponseData.current_task || gameState.currentTask
    });

    // Display the modal with feedback
    if (elements.taskFailureModal && elements.failureFeedback) {
        // Set the feedback text
        elements.failureFeedback.innerHTML = feedbackText || 'Your submission did not meet the requirements. Please try again.';

        // Add score information to the feedback
        const scoreInfo = `
            <div class="failure-score-info">
                <p>Your score: ${performanceScore}/10 (${similarityScore}%)</p>
                <p>You need a 'good' grade to pass this task.</p>
            </div>
        `;

        // Add the score info if it's not already there
        if (!elements.failureFeedback.innerHTML.includes('failure-score-info')) {
            elements.failureFeedback.innerHTML += scoreInfo;
        }

        // Show the modal
        elements.taskFailureModal.classList.remove('hidden');

        // Make sure the retry button is visible
        if (elements.retryTaskButton) {
            elements.retryTaskButton.style.display = 'block';
        }
    } else {
        // Fallback if modal elements aren't available
        console.error('Task failure modal elements not found');
        alert('Task failed. Please try again with a better response.');
    }
}

// Handle zoom preview button click
function handleZoomPreview() {
    if (!gameState.previewData) return;

    // Copy the preview content to the zoomed preview modal
    if (gameState.previewData.aiResponseHtml) {
        elements.zoomedPreviewContent.innerHTML = gameState.previewData.aiResponseHtml;
    } else {
        elements.zoomedPreviewContent.textContent = gameState.previewData.aiResponse;
    }

    // Show the zoom preview modal
    elements.zoomPreviewModal.classList.remove('hidden');
}

// Show loading overlay
function showLoading() {
    elements.loadingOverlay.classList.remove('hidden');
}

// Hide loading overlay
function hideLoading() {
    elements.loadingOverlay.classList.add('hidden');
}

// Helper function to capitalize first letter
function capitalizeFirstLetter(string) {
    return string.charAt(0).toUpperCase() + string.slice(1);
}

// Helper function to format timestamp
function formatTime(timestamp) {
    if (!timestamp) return '';

    try {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (e) {
        return '';
    }
}

// Helper function to generate a unique ID
function generateId() {
    return Math.random().toString(36).substring(2, 15);
}

// Handle promotion to a new role
function handlePromotion(newRole, completedRoles) {
    console.log(`Handling promotion to ${newRole} with completed roles:`, completedRoles);

    // Update game state with new role
    gameState.currentRole = newRole;

    // Update completed roles
    if (completedRoles && Array.isArray(completedRoles)) {
        gameState.completedRoles = completedRoles;
    }

    // BUGFIX: Reset role_challenges_completed to 0 after promotion
    gameState.roleChallengesCompleted = 0;
    console.log('BUGFIX - Reset roleChallengesCompleted to 0 in handlePromotion');

    // Force update the role progress display immediately
    if (elements.roleProgress) {
        let requiredChallenges = gameState.challengesRequired || 3;
        elements.roleProgress.textContent = `${gameState.roleChallengesCompleted}/${requiredChallenges}`;
        console.log('BUGFIX - Immediately updated role progress display in handlePromotion to:', elements.roleProgress.textContent);
    }

    // Make sure promotion messages are displayed prominently
    // This matches the Flask app's behavior
    console.log('Ensuring promotion messages are displayed prominently');

    // We no longer modify promotion messages after they're created
    // This ensures all promotion messages remain stable

    // Just ensure all promotion messages have the is_promotion flag set
    for (let i = gameState.messages.length - 1; i >= 0; i--) {
        const message = gameState.messages[i];
        if (message.text && message.text.includes('Congratulations') && message.sender === 'ceo') {
            // Set is_promotion flag for all promotion messages
            message.is_promotion = true;

            // Add CEO title to Joy Nantongo's name in all promotion messages if missing
            if (message.text.includes('Joy Nantongo') && !message.text.includes('Chief Executive Officer')) {
                message.text = message.text.replace('Joy Nantongo', 'Joy Nantongo, Chief Executive Officer (CEO)');
                if (message.html) {
                    message.html = message.html.replace('Joy Nantongo', 'Joy Nantongo, Chief Executive Officer (CEO)');
                }
                console.log('Added CEO title to Joy Nantongo in promotion message');
            }

            // Clean up any duplicate improvement sections if they exist
            if (message.html && message.html.indexOf('How to Improve Your Request:') !== message.html.lastIndexOf('How to Improve Your Request:')) {
                // Keep only the properly formatted improvement section
                const startPos = message.html.indexOf('<div class="improvement-section">');
                if (startPos > -1) {
                    const beforeSection = message.html.substring(0, startPos);
                    // Find the first occurrence of the improvement heading and remove it
                    const cleanedBefore = beforeSection.replace(/<h3>How to Improve Your Request:<\/h3>[\s\S]*?<\/ul>/, '');
                    message.html = cleanedBefore + message.html.substring(startPos);
                }
            }
        }
    }

    console.log('Ensured all promotion messages have proper flags and formatting');

    // BUGFIX: Force a refresh of the game state from the server
    console.log('BUGFIX - Fetching latest game state in handlePromotion');
    fetch(API_ENDPOINTS.GET_GAME_STATE)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Update local game state with server data
                if (data.current_role) gameState.currentRole = data.current_role;
                if (data.current_manager) gameState.currentManager = data.current_manager;
                if (data.current_task) gameState.currentTask = data.current_task;

                // BUGFIX: Properly handle role challenges completed count from server after promotion
                if (data.role_challenges_completed !== undefined) {
                    gameState.roleChallengesCompleted = data.role_challenges_completed;
                    console.log('BUGFIX - Updated roleChallengesCompleted from server in handlePromotion:', gameState.roleChallengesCompleted);
                } else {
                    // After promotion, this should be 0
                    gameState.roleChallengesCompleted = 0;
                    console.log('BUGFIX - Server did not provide roleChallengesCompleted after promotion, setting to 0');
                }

                // Force update the role progress display immediately
                if (elements.roleProgress) {
                    let requiredChallenges = gameState.challengesRequired || 3;
                    elements.roleProgress.textContent = `${gameState.roleChallengesCompleted}/${requiredChallenges}`;
                    console.log('BUGFIX - Immediately updated role progress display in handlePromotion to:', elements.roleProgress.textContent);
                }

                console.log('BUGFIX - Updated game state from server in handlePromotion:', {
                    currentRole: gameState.currentRole,
                    currentManager: gameState.currentManager,
                    currentTask: gameState.currentTask,
                    roleChallengesCompleted: gameState.roleChallengesCompleted
                });

                // Force update the UI to show the updated state
                updateUI();
            }
        })
        .catch(error => {
            console.error('Error fetching latest game state in handlePromotion:', error);
        });

    // Force update the organization chart after promotion
    console.log('Forcing org chart update after promotion');

    // Schedule multiple update attempts to ensure the chart updates
    setTimeout(() => {
        console.log('First attempt to update org chart after promotion');
        updateOrgChart();

        // Reinitialize chart zoom functionality
        if (window.reinitializeChartZoom) {
            console.log('Reinitializing chart zoom functionality');
            window.reinitializeChartZoom();
        }

        // BUGFIX: Only fetch the next task once after promotion
        // This is a critical part of the game progression
        console.log('BUGFIX - Setting up a single call to fetch next task after promotion');

        // Force nextTaskPending to true to ensure next task is fetched
        gameState.nextTaskPending = true;

        // Clear any existing timeout to prevent multiple calls
        if (window.nextTaskTimeout) {
            console.log('BUGFIX - Clearing existing nextTaskTimeout');
            clearTimeout(window.nextTaskTimeout);
        }

        // Set a direct timeout to ensure it's called only once
        window.nextTaskTimeout = setTimeout(() => {
            console.log('DEBUG - Timeout triggered in handlePromotion, calling fetchNextTask now');
            // Only call fetchNextTask if we're not already fetching
            if (!fetchingNextTask) {
                fetchNextTask();
            } else {
                console.log('BUGFIX - Already fetching next task, skipping duplicate call');
            }

            // Force update UI again
            updateUI();
        }, 3000); // 3 second delay

        // BUGFIX: Set a single timeout to fetch the next task after promotion
        // We don't need multiple attempts since we've fixed the task progression
        setTimeout(() => {
            console.log('BUGFIX - Checking if we need to fetch next task after promotion');
            if (gameState.nextTaskPending && !fetchingNextTask) {
                console.log('BUGFIX - Fetching next task after promotion');
                fetchNextTask();
            } else {
                console.log('BUGFIX - No need to fetch next task after promotion');
            }
        }, 5000); // 5 second delay

        // BUGFIX: Removed the manual next task button and all its logic
        // Instead, we'll rely on automatic task progression

        // Remove any existing manual next task button
        const manualNextTaskButton = document.getElementById('manual-next-task-button');
        if (manualNextTaskButton) {
            console.log('BUGFIX - Removing manual next task button');
            manualNextTaskButton.remove();
        }
    }, 500);

    setTimeout(() => {
        console.log('Second attempt to update org chart after promotion');
        if (window.forceUpdateOrgChart) {
            window.forceUpdateOrgChart(newRole);
        }

        // Reinitialize chart zoom functionality again
        if (window.reinitializeChartZoom) {
            console.log('Second attempt to reinitialize chart zoom functionality');
            window.reinitializeChartZoom();
        }
    }, 1500);

    setTimeout(() => {
        console.log('Third attempt to update org chart after promotion');
        // Force a page refresh of the chart by fetching fresh data
        fetch(API_ENDPOINTS.GET_GAME_STATE)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    console.log('Received fresh game state for org chart update');
                    if (data.org_chart_html) {
                        const mainOrgChart = document.getElementById('org-chart');
                        const mobileOrgChart = document.getElementById('mobile-org-chart');

                        if (mainOrgChart) {
                            console.log('Updating main org chart with server HTML');
                            mainOrgChart.innerHTML = data.org_chart_html;
                        }

                        if (mobileOrgChart) {
                            console.log('Updating mobile org chart with server HTML');
                            mobileOrgChart.innerHTML = data.org_chart_html;
                        }
                    } else {
                        console.log('No org chart HTML in response, generating manually');
                        const html = generateOrgChartHtml(data.current_role, data.completed_roles || []);
                        if (html) {
                            const mainOrgChart = document.getElementById('org-chart');
                            const mobileOrgChart = document.getElementById('mobile-org-chart');

                            if (mainOrgChart) {
                                console.log('Updating main org chart with generated HTML');
                                mainOrgChart.innerHTML = html;
                            }

                            if (mobileOrgChart) {
                                console.log('Updating mobile org chart with generated HTML');
                                mobileOrgChart.innerHTML = html;
                            }
                        }
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching fresh game state:', error);
            });
    }, 2500);
}

// Function to resend the current task message
function resendTaskMessage() {
    // Get the current task and manager
    const currentTask = gameState.currentTask;
    const currentManager = gameState.currentManager;

    console.log('Attempting to resend task message for task:', currentTask, 'from manager:', currentManager);

    // Find the last task message from the current manager
    // We need to search through all messages, including those that might be filtered out in the UI
    let taskMessage = null;
    for (let i = gameState.messages.length - 1; i >= 0; i--) {
        const message = gameState.messages[i];
        if (message.sender === currentManager && message.is_challenge && message.task_id === currentTask) {
            taskMessage = message;
            console.log('Found task message to resend:', message);
            break;
        }
    }

    // If we couldn't find the task message, try to fetch it from the server
    if (!taskMessage) {
        console.log('Could not find task message in current messages, attempting to fetch from server');

        // Make a request to the server to get the current task
        fetch(API_ENDPOINTS.GET_GAME_STATE)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Look for the task message in the server's messages
                    const serverMessages = data.messages || [];
                    for (let i = serverMessages.length - 1; i >= 0; i--) {
                        const message = serverMessages[i];
                        if (message.sender === currentManager && message.is_challenge && message.task_id === currentTask) {
                            console.log('Found task message from server:', message);

                            // Create a new message object with the same content
                            const resendMessage = {
                                id: generateId(),
                                sender: message.sender,
                                text: message.text,
                                html: message.html,
                                timestamp: new Date().toISOString(),
                                is_challenge: true,
                                task_id: message.task_id,
                                is_markdown: message.is_markdown || true,
                                is_resent: true // Mark as resent for styling purposes
                            };

                            // Add the message to the game state
                            gameState.messages.push(resendMessage);

                            // Update the UI and scroll to the new message
                            setStep('prompt');
                            updateUI();

                            // Scroll to the bottom and highlight the message
                            setTimeout(() => {
                                elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;

                                // Find the last message and highlight it
                                const messages = elements.messagesContainer.querySelectorAll('.message');
                                const lastMessage = messages[messages.length - 1];
                                if (lastMessage) {
                                    // Highlight the message briefly to draw attention
                                    lastMessage.classList.add('highlight-message');
                                    setTimeout(() => {
                                        lastMessage.classList.remove('highlight-message');
                                    }, 2000);
                                }
                            }, 100);

                            return;
                        }
                    }

                    // If we still couldn't find the task message, show an error
                    console.error('Could not find task message in server response');
                    alert('Could not find the current task. Please refresh the page and try again.');
                } else {
                    console.error('Failed to get game state from server');
                    alert('Failed to get current task. Please refresh the page and try again.');
                }
            })
            .catch(error => {
                console.error('Error fetching game state:', error);
                alert('Error connecting to the server. Please check your connection and try again.');
            });

        return false;
    }

    // If we found the task message, resend it
    console.log('Resending task message:', taskMessage);

    // Create a new message object with the same content but a new ID and timestamp
    const resendMessage = {
        id: generateId(),
        sender: taskMessage.sender,
        text: taskMessage.text,
        html: taskMessage.html,
        timestamp: new Date().toISOString(),
        is_challenge: true,
        task_id: taskMessage.task_id,
        is_markdown: taskMessage.is_markdown || true,
        is_resent: true // Mark as resent for styling purposes
    };

    // Add the message to the game state
    gameState.messages.push(resendMessage);

    // Make sure we're in the prompt step
    setStep('prompt');

    // Update the UI to show all messages including the new one
    updateUI();

    // Scroll to the bottom to show the new message
    setTimeout(() => {
        elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;

        // Find the last message and highlight it
        const messages = elements.messagesContainer.querySelectorAll('.message');
        const lastMessage = messages[messages.length - 1];
        if (lastMessage) {
            // Highlight the message briefly to draw attention
            lastMessage.classList.add('highlight-message');
            setTimeout(() => {
                lastMessage.classList.remove('highlight-message');
            }, 2000);
        }
    }, 100);

    return true;
}

// Setup UI interactions
function setupUIInteractions() {
    // Setup left sidebar toggle for all screen sizes
    const sidebarToggle = document.getElementById('sidebar-toggle');
    const sidebarCollapseIndicator = document.querySelector('.sidebar-collapse-indicator');

    // Function to toggle left sidebar - INDEPENDENT OPERATION
    const toggleLeftSidebar = () => {
        console.log('Left sidebar toggle clicked, window width:', window.innerWidth);
        if (window.innerWidth <= 992) {
            // On mobile, toggle left sidebar independently
            document.body.classList.toggle('sidebar-visible');
            console.log('Left sidebar toggled, body classes:', document.body.className);
        } else {
            // On desktop, use sidebar-hidden class
            document.body.classList.toggle('sidebar-hidden');
        }
        // Scroll to bottom of messages after sidebar toggle
        setTimeout(() => {
            if (elements.messagesContainer) {
                elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
            }
        }, 300);
    };

    // Add click event to left sidebar toggle button
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleLeftSidebar);
    }

    // Add click event to left sidebar collapse indicator
    if (sidebarCollapseIndicator) {
        sidebarCollapseIndicator.addEventListener('click', toggleLeftSidebar);
    }

    // Setup right sidebar toggle for all screen sizes
    const rightSidebarToggle = document.getElementById('right-sidebar-toggle');
    const rightSidebarCollapseIndicator = document.querySelector('.right-sidebar-collapse-indicator');

    // Function to toggle right sidebar - INDEPENDENT OPERATION
    const toggleRightSidebar = () => {
        console.log('Right sidebar toggle clicked, window width:', window.innerWidth);

        if (window.innerWidth <= 992) {
            // On mobile, toggle right sidebar independently - SAME LOGIC AS LEFT SIDEBAR
            document.body.classList.toggle('right-sidebar-visible');
            console.log('Mobile: Right sidebar toggled, body classes after:', document.body.className);

            // Check if right sidebar exists and log its computed styles
            const rightSidebar = document.querySelector('.right-sidebar');
            if (rightSidebar) {
                const styles = window.getComputedStyle(rightSidebar);
                console.log('Right sidebar computed styles:', {
                    right: styles.right,
                    transform: styles.transform,
                    display: styles.display,
                    visibility: styles.visibility,
                    opacity: styles.opacity
                });
            } else {
                console.log('ERROR: Right sidebar element not found!');
            }
        } else {
            // On desktop, use right-sidebar-hidden class
            document.body.classList.toggle('right-sidebar-hidden');
        }

        // Scroll to bottom of messages after sidebar toggle
        setTimeout(() => {
            if (elements.messagesContainer) {
                elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
            }
        }, 300);
    };

    // Add click event to right sidebar toggle button
    if (rightSidebarToggle) {
        rightSidebarToggle.addEventListener('click', toggleRightSidebar);
    }

    // Add click event to right sidebar collapse indicator
    if (rightSidebarCollapseIndicator) {
        rightSidebarCollapseIndicator.addEventListener('click', toggleRightSidebar);
    }

    // Keyboard shortcuts
    document.addEventListener('keydown', (e) => {
        // Alt+S for left sidebar
        if (e.altKey && e.key === 's') {
            e.preventDefault();
            toggleLeftSidebar();
        }
        // Alt+R for right sidebar
        if (e.altKey && e.key === 'r') {
            e.preventDefault();
            toggleRightSidebar();
        }
    });

    // Function to close mobile sidebars
    const closeMobileSidebars = () => {
        if (window.innerWidth <= 992) {
            document.body.classList.remove('sidebar-visible');
            document.body.classList.remove('right-sidebar-visible');
        }
    };

    // Add mobile sidebar close functionality
    const addMobileSidebarCloseHandlers = () => {
        if (window.innerWidth <= 992) {
            // Close sidebar when clicking the close button (::before pseudo-element)
            const sidebar = document.querySelector('.sidebar');
            const rightSidebar = document.querySelector('.right-sidebar');

            if (sidebar) {
                sidebar.addEventListener('click', (e) => {
                    const rect = sidebar.getBoundingClientRect();
                    const closeButtonArea = {
                        x: rect.right - 60, // 44px button + 16px margin
                        y: rect.top,
                        width: 60,
                        height: 60
                    };

                    if (e.clientX >= closeButtonArea.x && e.clientX <= closeButtonArea.x + closeButtonArea.width &&
                        e.clientY >= closeButtonArea.y && e.clientY <= closeButtonArea.y + closeButtonArea.height) {
                        closeMobileSidebars();
                    }
                });
            }

            if (rightSidebar) {
                rightSidebar.addEventListener('click', (e) => {
                    const rect = rightSidebar.getBoundingClientRect();
                    const closeButtonArea = {
                        x: rect.right - 60,
                        y: rect.top,
                        width: 60,
                        height: 60
                    };

                    if (e.clientX >= closeButtonArea.x && e.clientX <= closeButtonArea.x + closeButtonArea.width &&
                        e.clientY >= closeButtonArea.y && e.clientY <= closeButtonArea.y + closeButtonArea.height) {
                        closeMobileSidebars();
                    }
                });
            }
        }
    };

    // Mobile and tablet adjustments
    const isMobile = window.matchMedia('(max-width: 992px)').matches;
    const isTablet = window.matchMedia('(min-width: 769px) and (max-width: 992px)').matches;

    if (isMobile) {
        // On mobile, ensure sidebars are hidden by default
        document.body.classList.remove('sidebar-visible');
        document.body.classList.remove('right-sidebar-visible');
        document.body.classList.remove('sidebar-hidden');

        // FORCE SHOW RIGHT SIDEBAR TOGGLE - Override template inline script
        const rightToggle = document.getElementById('right-sidebar-toggle');
        if (rightToggle) {
            rightToggle.style.display = 'flex';
            rightToggle.style.visibility = 'visible';
            rightToggle.style.opacity = '1';
        }

        // Initialize mobile sidebar close handlers
        addMobileSidebarCloseHandlers();

        // Add escape key handler to close sidebars
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                closeMobileSidebars();
            }
        });

        // Enhanced mobile sidebar toggle functionality
        const mobileToggle = document.getElementById('sidebar-toggle');
        if (mobileToggle) {
            // Add touch-friendly interaction
            mobileToggle.addEventListener('touchstart', (e) => {
                e.preventDefault();
                mobileToggle.style.transform = 'scale(0.95)';
            });

            mobileToggle.addEventListener('touchend', (e) => {
                e.preventDefault();
                mobileToggle.style.transform = 'scale(1)';
                setTimeout(() => {
                    toggleLeftSidebar();
                }, 50);
            });
        }

        // Prevent double-tap zoom on buttons
        const allButtons = document.querySelectorAll('button');
        allButtons.forEach(button => {
            button.addEventListener('touchend', (e) => {
                e.preventDefault();
                // Trigger click after a short delay to prevent double-tap zoom
                setTimeout(() => {
                    button.click();
                }, 10);
            });
        });

        // Adjust textarea behavior on mobile
        const textareas = document.querySelectorAll('textarea');
        textareas.forEach(textarea => {
            // Auto-resize textareas based on content
            textarea.addEventListener('input', function() {
                this.style.height = 'auto';
                this.style.height = (this.scrollHeight) + 'px';
            });

            // Blur textarea when user taps outside
            document.addEventListener('touchstart', (e) => {
                if (!textarea.contains(e.target) && document.activeElement === textarea) {
                    textarea.blur();
                }
            });
        });

        // Ensure scrolling works properly in message container
        const messagesContainer = elements.messagesContainer;
        if (messagesContainer) {
            messagesContainer.style.webkitOverflowScrolling = 'touch'; // Enables momentum scrolling on iOS
        }
    } else {
        // On desktop, show both sidebars by default
        document.body.classList.remove('sidebar-hidden');
        document.body.classList.remove('right-sidebar-hidden');
    }
}

// Show restart game confirmation modal
function showRestartConfirmation() {
    console.log('Showing restart confirmation modal');
    const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
    if (restartConfirmationModal) {
        restartConfirmationModal.classList.remove('hidden');

        // Make sure the confirm button has the correct event listener
        const confirmRestartButton = document.getElementById('confirm-restart-button');
        if (confirmRestartButton) {
            // Remove any existing event listeners to prevent duplicates
            const newConfirmButton = confirmRestartButton.cloneNode(true);
            confirmRestartButton.parentNode.replaceChild(newConfirmButton, confirmRestartButton);

            // Add the event listener
            newConfirmButton.addEventListener('click', () => {
                console.log('Restart confirmed by user');
                restartConfirmationModal.classList.add('hidden');

                // Clear UI messages immediately
                if (elements.messagesContainer) {
                    elements.messagesContainer.innerHTML = '';
                    console.log('Cleared message container in UI after restart confirmation');
                }

                // Reset game state messages array
                gameState.messages = [];
                console.log('Reset gameState.messages array after restart confirmation');

                // Start a new game with cache-busting
                startGame();
            });
        }
    }
}

// Function to enhance CEO names in the document
function enhanceCEONames() {
    // Find all elements that might contain the CEO name
    const elements = document.querySelectorAll('*');

    elements.forEach(element => {
        if (element.textContent &&
            element.textContent.includes('Joy Nantongo') &&
            element.childNodes.length === 1 &&
            element.childNodes[0].nodeType === Node.TEXT_NODE) {

            // Check if it's in the header
            if (element.closest('.header')) {
                // Create a new span with the CEO header name class
                const ceoSpan = document.createElement('span');
                ceoSpan.className = 'ceo-header-name';
                ceoSpan.textContent = element.textContent;

                // Replace the text node with the span
                element.textContent = '';
                element.appendChild(ceoSpan);
            }
        }
    });
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    // Initialize visualizations immediately if possible

    // Initialize role progression content
    if (elements.roleProgressionContent) {
        console.log('Setting initial placeholder for role progression on DOMContentLoaded');
        elements.roleProgressionContent.innerHTML = '<div class="role-progression-placeholder">Career progression visualization loading...</div>';
    }

    // Initialize org chart content
    if (elements.orgChart) {
        console.log('Setting initial placeholder for org chart on DOMContentLoaded');
        elements.orgChart.innerHTML = '<div class="org-chart-placeholder">Organization chart loading...</div>';
    }

    // Check if we're coming from a restart action
    const urlParams = new URLSearchParams(window.location.search);
    const isRestart = urlParams.get('restart') === 'true';

    if (isRestart) {
        console.log('Restart parameter detected in URL, starting new game...');
        // Clear any cached messages in the UI
        if (elements.messagesContainer) {
            elements.messagesContainer.innerHTML = '';
        }
        // Reset game state
        gameState.messages = [];
        // Start a new game
        startGame();
        return;
    }

    // Try to fetch game state directly
    fetch(API_ENDPOINTS.GET_GAME_STATE)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                console.log('Received initial game state');

                // Update role progression
                if (elements.roleProgressionContent) {
                    if (data.role_progression_html) {
                        console.log('Setting role progression HTML from initial fetch');
                        elements.roleProgressionContent.innerHTML = data.role_progression_html;
                    } else {
                        console.log('No role progression HTML in initial fetch, generating manually');
                        const html = generateRoleProgressionHtml(data.current_role, data.completed_roles || []);
                        if (html) {
                            elements.roleProgressionContent.innerHTML = html;
                        }
                    }
                }

                // Update org chart
                if (elements.orgChart) {
                    if (data.org_chart_html) {
                        console.log('Setting org chart HTML from initial fetch');
                        elements.orgChart.innerHTML = data.org_chart_html;
                    } else {
                        console.log('No org chart HTML in initial fetch, generating manually');
                        const html = generateOrgChartHtml(data.current_role, data.completed_roles || []);
                        if (html) {
                            elements.orgChart.innerHTML = html;
                        }
                    }
                }
            }
        })
        .catch(error => {
            console.error('Error fetching initial game state:', error);
        });

    initGame();
    setupUIInteractions();

    // Listen for orientation changes and resize events
    window.addEventListener('resize', () => {
        // Update UI for new screen dimensions
        if (elements.messagesContainer) {
            elements.messagesContainer.scrollTop = elements.messagesContainer.scrollHeight;
        }
    });

    // Fix for iOS viewport height issues
    const setVH = () => {
        let vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    };

    setVH();
    window.addEventListener('resize', setVH);

    // Enhance CEO names in the document
    enhanceCEONames();

    // Set up a mutation observer to enhance CEO names when the DOM changes
    const observer = new MutationObserver(function(mutations) {
        enhanceCEONames();
    });

    // Start observing the document with the configured parameters
    observer.observe(document.body, { childList: true, subtree: true });
});
