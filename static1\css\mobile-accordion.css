/**
 * Mobile Accordion Sidebar CSS
 * Ensures proper accordion behavior for mobile sidebars
 */

/* Mobile-specific accordion styles */
@media (max-width: 768px) {
    /* Ensure toggle buttons are always visible in header */
    .mobile-sidebar-toggle,
    #sidebar-toggle,
    .right-sidebar-toggle,
    #right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1051 !important;
        background: var(--primary-color, #667eea) !important;
        color: white !important;
        border: none !important;
        border-radius: 8px !important;
        width: 44px !important;
        height: 44px !important;
        align-items: center !important;
        justify-content: center !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
    }

    .mobile-sidebar-toggle:hover,
    #sidebar-toggle:hover,
    .right-sidebar-toggle:hover,
    #right-sidebar-toggle:hover {
        background: var(--primary-dark, #5a67d8) !important;
        transform: scale(1.05) !important;
    }

    /* Hamburger menu lines */
    .mobile-sidebar-toggle span,
    #sidebar-toggle span,
    .right-sidebar-toggle span,
    #right-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* Header layout for mobile */
    .header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 12px 16px !important;
        background: var(--bg-secondary, #f8f9fa) !important;
        border-bottom: 1px solid var(--border-primary, #e0e0e0) !important;
        position: sticky !important;
        top: 0 !important;
        z-index: 1050 !important;
        min-height: 60px !important;
    }

    /* Role display in center */
    .role-display {
        flex: 1 !important;
        text-align: center !important;
        font-weight: 600 !important;
        color: var(--text-primary, #333) !important;
    }

    /* Remove individual section accordion styling - entire sidebar is now the accordion */
    .sidebar .sidebar-heading,
    .right-sidebar .sidebar-heading,
    .sidebar h6,
    .right-sidebar h6 {
        cursor: default !important;
        position: relative !important;
        padding: 12px 16px !important;
        background: none !important;
        border-radius: 0 !important;
        margin-bottom: 16px !important;
        transition: none !important;
        user-select: auto !important;
    }

    /* Ensure all sidebar content is visible when expanded */
    .sidebar .mb-4 ul,
    .right-sidebar .mb-4 ul {
        display: block !important;
        max-height: none !important;
        overflow: visible !important;
        padding-left: 16px !important;
        margin-top: 8px !important;
        transition: none !important;
    }

    /* Ensure proper spacing for mobile */
    .sidebar .nav-link,
    .right-sidebar .nav-link {
        padding: 8px 12px !important;
        margin-bottom: 4px !important;
        border-radius: 6px !important;
        font-size: 14px !important;
    }

    /* Mobile sidebar content padding */
    .sidebar > *,
    .right-sidebar > * {
        padding-left: 16px !important;
        padding-right: 16px !important;
    }

    .sidebar-header,
    .right-sidebar-header {
        padding: 16px !important;
        margin-bottom: 0 !important;
    }

    /* Shortcut hints */
    .shortcut-hint {
        position: absolute !important;
        bottom: -20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        font-size: 10px !important;
        color: rgba(255, 255, 255, 0.7) !important;
        background: rgba(0, 0, 0, 0.5) !important;
        padding: 2px 6px !important;
        border-radius: 4px !important;
        white-space: nowrap !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
    }

    .mobile-sidebar-toggle:hover .shortcut-hint,
    .right-sidebar-toggle:hover .shortcut-hint {
        opacity: 1 !important;
    }
}

/* Ensure proper layout order for single sidebar accordions */
@media (max-width: 768px) {
    .app-container {
        display: flex !important;
        flex-direction: column !important;
        min-height: 100vh !important;
        height: auto !important;
        overflow-x: hidden !important;
        overflow-y: auto !important;
    }

    /* Layout order: sidebar -> header -> main content -> right sidebar */
    .sidebar {
        order: 0 !important;
        flex-shrink: 0 !important; /* Don't shrink when expanded */
    }

    .header {
        order: 1 !important;
        flex-shrink: 0 !important; /* Always maintain header size */
    }

    .main-content {
        order: 2 !important;
        flex: 1 !important;
        min-height: 300px !important; /* Ensure main content is always visible */
    }

    .right-sidebar {
        order: 3 !important;
        flex-shrink: 0 !important; /* Don't shrink when expanded */
    }

    /* Ensure sidebars start collapsed by default */
    body:not(.sidebar-visible) .sidebar {
        height: 0 !important;
        max-height: 0 !important;
        padding: 0 !important;
        opacity: 0 !important;
    }

    body:not(.right-sidebar-visible) .right-sidebar {
        height: 0 !important;
        max-height: 0 !important;
        padding: 0 !important;
        opacity: 0 !important;
    }
}
